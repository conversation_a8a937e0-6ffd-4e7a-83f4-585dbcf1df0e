#!/usr/bin/env python3
"""
Simple test script to check imports
"""
import sys
import os

print("Python version:", sys.version)
print("Current working directory:", os.getcwd())
print("Python path:", sys.path[:3])  # Show first 3 entries

# Test basic imports
try:
    import numpy
    print("✓ numpy import OK")
except ImportError as e:
    print("✗ numpy import failed:", e)

try:
    import cv2
    print("✓ opencv import OK")
except ImportError as e:
    print("✗ opencv import failed:", e)

try:
    import torch
    print("✓ torch import OK")
except ImportError as e:
    print("✗ torch import failed:", e)

try:
    import scipy
    print("✓ scipy import OK")
except ImportError as e:
    print("✗ scipy import failed:", e)

try:
    from ultralytics import YOLO
    print("✓ ultralytics import OK")
except ImportError as e:
    print("✗ ultralytics import failed:", e)

# Test our module
try:
    from deepsort_tracker.config import DetectionConfig
    print("✓ DetectionConfig import OK")
except ImportError as e:
    print("✗ DetectionConfig import failed:", e)

try:
    from deepsort_tracker.config import Config
    print("✓ Config import OK")
    config = Config()
    print("✓ Config creation OK")
except Exception as e:
    print("✗ Config import/creation failed:", e)
    import traceback
    traceback.print_exc()

print("Test completed!")
