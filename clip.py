from typing import Dict, List
from clip_client import Client
import numpy as np
import weaviate
from settings import env, OllamaBody, SmartCity, schema
from utils import image2base64, Object
from llama_index.llms.ollama import Ollama
from llama_index.vector_stores.milvus import MilvusVectorStore
from llama_index.core.schema import ImageDocument
import requests
import cv2
from pymilvus import MilvusClient
from pymilvus.exceptions import MilvusException
import json



class IndexerClient:
    def __init__(self) -> None:
        self.clip_server = Client(env.clip_host)
        self.vector_db = MilvusClient(
            uri="./demo.db"
        )
        try:
            self.vector_db.get_collection_stats(env.index_description)
        except MilvusException as milvusErr:
            if milvusErr.code == 100:
                print(f'{env.index_description} not exist. Create ones')
                self.vector_db.create_collection(env.index_description,1024, auto_id=True)

        try:
            self.vector_db.get_collection_stats(env.index_camera)
        except MilvusException as milvusErr:
            if milvusErr.code == 100:
                print(f'{env.index_camera} not exist. Create ones')
                self.vector_db.create_collection(env.index_camera,512, auto_id=True)

        try:
            self.vector_db.get_collection_stats(env.index_obj)
        except MilvusException as milvusErr:
            if milvusErr.code == 100:
                print(f'{env.index_obj} not exist. Create ones')
                self.vector_db.create_collection(env.index_obj,512, auto_id=True)

        
        
    
    def index_obj(self, image, bboxes: List[Object], camera_id: int):
        objs = [image2base64(b.crop(image)) for b in bboxes]
        results = self.clip_server.encode(objs)
        data = [SmartCity(timestamp=b.index, cameraid=camera_id,vector=r.tolist()).model_dump() for r,b in zip(results, bboxes)]
        res = self.vector_db.insert(collection_name=env.index_obj,data=data)

        

    
    def index_image(self, image, timestamp: int, camera_id: int):
        img_b64 = image2base64(image)
        results = self.clip_server.encode([img_b64])
        data = [SmartCity(timestamp=timestamp, cameraid=camera_id,vector=r.tolist()).model_dump() for r in results]
        res = self.vector_db.insert(collection_name=env.index_obj,data=data)
        print('TEST')

    def gen_description(self, image: np.ndarray, prompt: str):
        img_b64 = image2base64(image,add_mark=False)
        body = OllamaBody(prompt=prompt,images=[img_b64])
        rep = requests.post(env.ollama_host + '/api/generate',data=body.model_dump_json(),headers={'Content-Type':'text/plain'})
        if rep.status_code != 200:
            print('[ERR]')
        raw_answer = rep.text.split('\n')
        list_char = [json.loads(r)['response'] for r in raw_answer[:-3]]
        results = ''.join(list_char)
        return results
        

    def embedding_description(self, description: str):
        body = OllamaBody(model='mxbai-embed-large', prompt=description)
        rep = requests.post(env.ollama_host + '/api/embeddings',data=body.model_dump_json(exclude_none=True),headers={'Content-Type':'text/plain'})
        if rep.status_code != 200:
            print('[ERR]')
        embed = rep.json()['embedding']
        return embed


img = cv2.imread('test/0002_c1s1_000451_03.jpg')
a = IndexerClient()
b = a.index_image(img, timestamp=30, camera_id=0)
c = a.embedding_description(b)
print(len(c))