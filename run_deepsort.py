#!/usr/bin/env python3
"""
Simple runner script for the DeepSORT tracking system
"""
import os
import sys
import argparse


def main():
    """Main runner function"""
    parser = argparse.ArgumentParser(description='DeepSORT Multi-Object Tracking System')
    
    # Add subcommands
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Track command
    track_parser = subparsers.add_parser('track', help='Run tracking on video')
    track_parser.add_argument('--video', type=str, required=True, help='Path to input video file')
    track_parser.add_argument('--output', type=str, default='output/tracked_video.mp4', help='Output video path')
    track_parser.add_argument('--model', type=str, default='yolov8n.pt', help='YOLO model to use')
    track_parser.add_argument('--confidence', type=float, default=0.5, help='Detection confidence threshold')
    track_parser.add_argument('--device', type=str, default='cuda', choices=['cuda', 'cpu'], help='Device to use')
    track_parser.add_argument('--no-display', action='store_true', help='Disable video display')
    track_parser.add_argument('--no-multiprocess', action='store_true', help='Disable multiprocessing')
    track_parser.add_argument('--max-frames', type=int, help='Maximum frames to process')
    
    # Webcam command
    webcam_parser = subparsers.add_parser('webcam', help='Run tracking on webcam')
    webcam_parser.add_argument('--output', type=str, default='output/webcam_tracking.mp4', help='Output video path')
    webcam_parser.add_argument('--model', type=str, default='yolov8n.pt', help='YOLO model to use')
    webcam_parser.add_argument('--confidence', type=float, default=0.6, help='Detection confidence threshold')
    webcam_parser.add_argument('--device', type=str, default='cuda', choices=['cuda', 'cpu'], help='Device to use')
    webcam_parser.add_argument('--no-display', action='store_true', help='Disable video display')
    
    # Test command
    test_parser = subparsers.add_parser('test', help='Run system tests')
    test_parser.add_argument('--component', type=str, choices=['detector', 'tracker', 'webcam', 'all'], 
                           default='all', help='Component to test')
    
    # Example command
    example_parser = subparsers.add_parser('example', help='Run example scenarios')
    example_parser.add_argument('--scenario', type=str, 
                               choices=['video', 'webcam', 'accuracy', 'performance', 'all'],
                               default='video', help='Example scenario to run')
    
    # Install command
    install_parser = subparsers.add_parser('install', help='Install dependencies and download models')
    install_parser.add_argument('--models', action='store_true', help='Download required models')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # Execute commands
    if args.command == 'track':
        run_tracking(args)
    elif args.command == 'webcam':
        run_webcam(args)
    elif args.command == 'test':
        run_tests(args)
    elif args.command == 'example':
        run_examples(args)
    elif args.command == 'install':
        run_install(args)


def run_tracking(args):
    """Run video tracking"""
    print(f"Running tracking on video: {args.video}")
    
    # Check if video exists
    if not os.path.exists(args.video):
        print(f"Error: Video file not found: {args.video}")
        return
    
    # Build command
    cmd_args = [
        sys.executable, '-m', 'deepsort_tracker.main',
        '--video', args.video,
        '--output', args.output,
        '--log-level', 'INFO'
    ]
    
    if args.no_display:
        cmd_args.append('--no-display')
    if args.no_multiprocess:
        cmd_args.append('--no-multiprocess')
    if args.max_frames:
        cmd_args.extend(['--max-frames', str(args.max_frames)])
    
    # Set environment variables
    os.environ['YOLO_MODEL_PATH'] = args.model
    os.environ['CONFIDENCE_THRESHOLD'] = str(args.confidence)
    os.environ['DETECTION_DEVICE'] = args.device
    
    # Run command
    import subprocess
    try:
        subprocess.run(cmd_args, check=True)
        print(f"✓ Tracking completed! Output saved to: {args.output}")
    except subprocess.CalledProcessError as e:
        print(f"✗ Tracking failed with error code: {e.returncode}")
    except KeyboardInterrupt:
        print("\n✗ Tracking interrupted by user")


def run_webcam(args):
    """Run webcam tracking"""
    print("Running webcam tracking...")
    
    # Build command
    cmd_args = [
        sys.executable, '-m', 'deepsort_tracker.main',
        '--output', args.output,
        '--log-level', 'INFO'
    ]
    
    if args.no_display:
        cmd_args.append('--no-display')
    
    # Set environment variables
    os.environ['YOLO_MODEL_PATH'] = args.model
    os.environ['CONFIDENCE_THRESHOLD'] = str(args.confidence)
    os.environ['DETECTION_DEVICE'] = args.device
    os.environ['NUM_DETECTION_WORKERS'] = '1'  # Less workers for real-time
    
    # Run command
    import subprocess
    try:
        print("Press Ctrl+C to stop webcam tracking")
        subprocess.run(cmd_args, check=True)
        print(f"✓ Webcam tracking completed! Output saved to: {args.output}")
    except subprocess.CalledProcessError as e:
        print(f"✗ Webcam tracking failed with error code: {e.returncode}")
    except KeyboardInterrupt:
        print("\n✓ Webcam tracking stopped by user")


def run_tests(args):
    """Run system tests"""
    print(f"Running tests for: {args.component}")
    
    try:
        from deepsort_tracker.test_system import (
            test_detector, test_feature_extractor, test_tracker, 
            test_webcam, run_all_tests
        )
        
        if args.component == 'detector':
            test_detector()
        elif args.component == 'tracker':
            test_tracker()
        elif args.component == 'webcam':
            test_webcam()
        elif args.component == 'all':
            run_all_tests()
        
    except ImportError as e:
        print(f"✗ Failed to import test modules: {e}")
    except Exception as e:
        print(f"✗ Test failed: {e}")


def run_examples(args):
    """Run example scenarios"""
    print(f"Running example: {args.scenario}")
    
    try:
        from deepsort_tracker.example import (
            example_video_tracking, example_webcam_tracking,
            example_high_accuracy_tracking, example_performance_optimized
        )
        
        if args.scenario == 'video':
            example_video_tracking()
        elif args.scenario == 'webcam':
            example_webcam_tracking()
        elif args.scenario == 'accuracy':
            example_high_accuracy_tracking()
        elif args.scenario == 'performance':
            example_performance_optimized()
        elif args.scenario == 'all':
            print("Running all examples...")
            example_video_tracking()
            example_webcam_tracking()
            example_high_accuracy_tracking()
            example_performance_optimized()
        
    except ImportError as e:
        print(f"✗ Failed to import example modules: {e}")
    except Exception as e:
        print(f"✗ Example failed: {e}")


def run_install(args):
    """Install dependencies and download models"""
    print("Installing dependencies and models...")
    
    import subprocess
    
    # Install Python dependencies
    print("Installing Python dependencies...")
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'deepsort_tracker/requirements.txt'], 
                      check=True)
        print("✓ Python dependencies installed")
    except subprocess.CalledProcessError:
        print("✗ Failed to install Python dependencies")
        return
    
    # Download models if requested
    if args.models:
        print("Downloading models...")
        
        # Create weights directory
        os.makedirs('weights', exist_ok=True)
        
        # Download YOLO models (they will be downloaded automatically on first use)
        print("YOLO models will be downloaded automatically on first use")
        
        # Note: Feature extraction model needs to be downloaded manually
        print("Note: Please download the feature extraction model manually:")
        print("  - ResNet50 model for person re-identification")
        print("  - Place it in: weights/resnet50_market1501_aicity156.onnx")
        print("  - You can find models at: https://github.com/mikel-brostrom/Yolov5_DeepSort_Pytorch")
        
        print("✓ Model setup instructions provided")


if __name__ == "__main__":
    main()
