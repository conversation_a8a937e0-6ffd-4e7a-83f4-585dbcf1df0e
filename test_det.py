from inference import Inference
import cv2
import torch
import torchvision.transforms as TF
import torchvision
import numpy as np
from torchvision.utils import draw_bounding_boxes, save_image, draw_keypoints
from torchvision.io import read_image, ImageReadMode

def distance2kps(points, distance, max_shape=None):
    """Decode distance prediction to bounding box.

    Args:
        points (Tensor): Shape (n, 2), [x, y].
        distance (Tensor): Distance from the given point to 4
            boundaries (left, top, right, bottom).
        max_shape (tuple): Shape of the image.

    Returns:
        Tensor: Decoded bboxes.
    """
    preds = []
    for i in range(0, distance.shape[1], 2):
        px = points[:, i%2] + distance[:, i]
        py = points[:, i%2+1] + distance[:, i+1]
        if max_shape is not None:
            px = px.clamp(min=0, max=max_shape[1])
            py = py.clamp(min=0, max=max_shape[0])
        preds.append(px)
        preds.append(py)
    return torch.stack(preds, axis=-1)

def distance2bbox(points, distance, max_shape=None):
    """Decode distance prediction to bounding box.

    Args:
        points (Tensor): Shape (n, 2), [x, y].
        distance (Tensor): Distance from the given point to 4
            boundaries (left, top, right, bottom).
        max_shape (tuple): Shape of the image.

    Returns:
        Tensor: Decoded bboxes.
    """
    x1 = points[:, 0] - distance[:, 0]
    y1 = points[:, 1] - distance[:, 1]
    x2 = points[:, 0] + distance[:, 2]
    y2 = points[:, 1] + distance[:, 3]
    if max_shape is not None:
        x1 = x1.clamp(min=0, max=max_shape[1])
        y1 = y1.clamp(min=0, max=max_shape[0])
        x2 = x2.clamp(min=0, max=max_shape[1])
        y2 = y2.clamp(min=0, max=max_shape[0])
    return torch.stack([x1, y1, x2, y2], axis=-1)

def clip_boxes(boxes, shape):
    """
    Takes a list of bounding boxes and a shape (height, width) and clips the bounding boxes to the shape.

    Args:
        boxes (torch.Tensor): the bounding boxes to clip
        shape (tuple): the shape of the image

    Returns:
        (torch.Tensor | numpy.ndarray): Clipped boxes
    """
    if isinstance(boxes, torch.Tensor):  # faster individually (WARNING: inplace .clamp_() Apple MPS bug)
        boxes[..., 0] = boxes[..., 0].clamp(0, shape[1])  # x1
        boxes[..., 1] = boxes[..., 1].clamp(0, shape[0])  # y1
        boxes[..., 2] = boxes[..., 2].clamp(0, shape[1])  # x2
        boxes[..., 3] = boxes[..., 3].clamp(0, shape[0])  # y2
    else:  # np.array (faster grouped)
        boxes[..., [0, 2]] = boxes[..., [0, 2]].clip(0, shape[1])  # x1, x2
        boxes[..., [1, 3]] = boxes[..., [1, 3]].clip(0, shape[0])  # y1, y2
    return boxes

def scale_boxes(img1_shape, boxes, img0_shape, ratio_pad=None, padding=True, xywh=False):
    """
    Rescales bounding boxes (in the format of xyxy by default) from the shape of the image they were originally
    specified in (img1_shape) to the shape of a different image (img0_shape).

    Args:
        img1_shape (tuple): The shape of the image that the bounding boxes are for, in the format of (height, width).
        boxes (torch.Tensor): the bounding boxes of the objects in the image, in the format of (x1, y1, x2, y2)
        img0_shape (tuple): the shape of the target image, in the format of (height, width).
        ratio_pad (tuple): a tuple of (ratio, pad) for scaling the boxes. If not provided, the ratio and pad will be
            calculated based on the size difference between the two images.
        padding (bool): If True, assuming the boxes is based on image augmented by yolo style. If False then do regular
            rescaling.
        xywh (bool): The box format is xywh or not, default=False.

    Returns:
        boxes (torch.Tensor): The scaled bounding boxes, in the format of (x1, y1, x2, y2)
    """
    if ratio_pad is None:  # calculate from img0_shape
        gain = min(img1_shape[0] / img0_shape[0], img1_shape[1] / img0_shape[1])  # gain  = old / new
        pad = (
            round((img1_shape[1] - img0_shape[1] * gain) / 2 - 0.1),
            round((img1_shape[0] - img0_shape[0] * gain) / 2 - 0.1),
        )  # wh padding
    else:
        gain = ratio_pad[0][0]
        pad = ratio_pad[1]

    if padding:
        boxes[..., 0] -= pad[0]  # x padding
        boxes[..., 1] -= pad[1]  # y padding
        if not xywh:
            boxes[..., 2] -= pad[0]  # x padding
            boxes[..., 3] -= pad[1]  # y padding
    boxes[..., :4] /= gain
    return clip_boxes(boxes, img0_shape)

im = cv2.imread('test/hang_10.jpg')
H,W,C = im.shape
im_draw = read_image('test/hang_10.jpg', ImageReadMode.RGB)

input_size = tuple(im.shape[0:2][::-1])
det = Inference('weights/det_10g.onnx', inputs=[[1,3,640,640]])
i = cv2.dnn.blobFromImage(im,1/128,input_size, (127.5,127.5,127.5), swapRB=True)
t = im.copy()[...,::-1]
t = torch.tensor(t.copy()).permute(2,0,1).to(dtype=torch.float32)
tr = TF.Compose([
    TF.Resize((640,640)),
    TF.Normalize(mean=127.5, std=128),
    TF.Lambda(lambda x: x.unsqueeze(0)),
])
l = tr(t).to(device=torch.device('cuda:0'))
net_outs = det(l)
fmc = 3
_feat_stride_fpn = [8, 16, 32]
_num_anchors = 2
input_height = l.shape[2]
input_width = l.shape[3]
use_kps = False
center_cache = {}
scores_list = []
bboxes_list = []
kpss_list = []
threshold = 0.5
for idx, stride in enumerate(_feat_stride_fpn):
    scores = net_outs[idx]
    bbox_preds = net_outs[idx+fmc]
    bbox_preds = bbox_preds * stride
    kps_preds = net_outs[idx+fmc*2] * stride
    height = input_height // stride
    width = input_width // stride
    K = height * width
    key = (height, width, stride)
    if key in center_cache:
        anchor_centers = center_cache[key]
    else:
        #solution-1, c style:
        #anchor_centers = np.zeros( (height, width, 2), dtype=np.float32 )
        #for i in range(height):
        #    anchor_centers[i, :, 1] = i
        #for i in range(width):
        #    anchor_centers[:, i, 0] = i

        #solution-2:
        #ax = np.arange(width, dtype=np.float32)
        #ay = np.arange(height, dtype=np.float32)
        #xv, yv = np.meshgrid(np.arange(width), np.arange(height))
        #anchor_centers = np.stack([xv, yv], axis=-1).astype(np.float32)

        #solution-3:
        anchor_centers = np.stack(np.mgrid[:height, :width][::-1], axis=-1).astype(np.float32)
        #print(anchor_centers.shape)

        anchor_centers = (anchor_centers * stride).reshape( (-1, 2) )
        if _num_anchors>1:
            anchor_centers = np.stack([anchor_centers]*_num_anchors, axis=1).reshape( (-1,2) )
        if len(center_cache)<100:
            center_cache[key] = anchor_centers
        anchor_centers = torch.from_numpy(anchor_centers).to(torch.device('cuda:0'))

    pos_inds = torch.where(scores>=threshold)[0]
    bboxes = distance2bbox(anchor_centers, bbox_preds)
    # bboxes = bbox_preds
    pos_scores = scores[pos_inds]
    pos_bboxes = bboxes[pos_inds]
    scores_list.append(pos_scores)
    bboxes_list.append(pos_bboxes)
    kpss = distance2kps(anchor_centers, kps_preds)
    #kpss = kps_preds
    kpss = kpss.reshape( (kpss.shape[0], -1, 2) )
    pos_kpss = kpss[pos_inds]
    kpss_list.append(pos_kpss)


scores = torch.vstack(scores_list)
bboxes = torch.vstack(bboxes_list)
kpss = torch.vstack(kpss_list)

k = torchvision.ops.nms(bboxes,scores.flatten(),0.5)

bboxes = bboxes[k]
scores = scores[k]
kpss = kpss[k]

bboxes = bboxes / torch.tensor([640,640,640,640], device=torch.device('cuda:0')) * torch.tensor([W,H,W,H], device=torch.device('cuda:0'))
bboxes = bboxes.to(dtype=torch.int32)
kpss  = kpss / torch.tensor([640/W,640/H], device=torch.device('cuda:0'))
kpss = kpss.to(dtype=torch.int32)
test = draw_bounding_boxes(im_draw,bboxes)
test = draw_keypoints(test, kpss, colors="green", radius=5)
save_image(test/255, 'test/face_test_bb.png')


print('test')
