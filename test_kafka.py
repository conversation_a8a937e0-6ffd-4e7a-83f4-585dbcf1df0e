from confluent_kafka import Consumer
import json
import boto3
from botocore.exceptions import ClientError
from clip_client import Client
import weaviate

client = Client('grpc://clip-service.milvus:51000')
db = weaviate.connect_to_local(
    host='weaviate.weaviate',
    port=80,
    grpc_port=50051
    )
collection = db.collections.get("smart-city")
c = Consumer({
    'bootstrap.servers': 'kafka-0-internal.kafka:9092,kafka-1-internal.kafka:9092,kafka-2-internal.kafka:9092',
    'group.id': 'consumer-smart-city',
    'auto.offset.reset': 'earliest'
})

c.subscribe(['smart-city'])

s3_client = boto3.client('s3',
                         endpoint_url='http://vector-hl.milvus:9000',
                         aws_access_key_id='dDr8P04CEeTWdtFMUGzv',
                         aws_secret_access_key='7NQntVvU101pTaeLYpj7UZ9I93C0PkbMzpBNWtcW',
                         )

def create_presigned_url(bucket_name, object_name, expiration=3600):
    """Generate a presigned URL to share an S3 object

    :param bucket_name: string
    :param object_name: string
    :param expiration: Time in seconds for the presigned URL to remain valid
    :return: Presigned URL as string. If error, returns None.
    """
    try:
        response = s3_client.generate_presigned_url('get_object',
                                                    Params={'Bucket': bucket_name,
                                                            'Key': object_name},
                                                    ExpiresIn=expiration)
    except ClientError as e:
        print(e)
        return None
    return response


while True:
    msg = c.poll(1.0)

    if msg is None:
        continue
    if msg.error():
        print("Consumer error: {}".format(msg.error()))
        continue
    data = json.loads(msg.value().decode('utf-8'))
    img_path = data['Key']
    method = data['EventName']
    record = data['Records'][0]
    bucket = record['s3']['bucket']['name']
    key:str = record['s3']['object']['key']
    name = key.split('.')[0]
    
    camera_id = name.split('_')[0]
    timestamp = name.split('_')[1]
    
    if method.split(':')[-1] == 'Put':
        #create
        presign_url = create_presigned_url(bucket,key, expiration=86400)
        r = client.encode(
            [
                presign_url
            ]
        )
        if len(r) > 0:
            collection.data.insert(properties={
                    'camera_id':camera_id,
                    'key':key,
                    'timestamp':timestamp
                },
                vector=r[0]
            )
    else:
        #delete
        pass
    print('Received message: {}'.format(msg.value().decode('utf-8')))

c.close()