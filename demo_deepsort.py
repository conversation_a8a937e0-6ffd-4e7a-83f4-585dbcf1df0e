#!/usr/bin/env python3
"""
Demo script for DeepSORT tracking system
This script demonstrates the core functionality without requiring all dependencies
"""
import cv2
import numpy as np
import time
import os

def create_demo_video():
    """Create a simple demo video with moving rectangles (simulating people)"""
    print("Creating demo video...")
    
    # Video properties
    width, height = 640, 480
    fps = 30
    duration = 10  # seconds
    total_frames = fps * duration
    
    # Create video writer
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter('demo_input.mp4', fourcc, fps, (width, height))
    
    # Create moving objects (simulating people)
    objects = [
        {'pos': [50, 100], 'vel': [2, 1], 'size': [40, 80], 'color': (0, 255, 0)},
        {'pos': [200, 150], 'vel': [-1, 2], 'size': [35, 75], 'color': (255, 0, 0)},
        {'pos': [400, 200], 'vel': [1, -1], 'size': [45, 85], 'color': (0, 0, 255)},
    ]
    
    for frame_num in range(total_frames):
        # Create frame
        frame = np.zeros((height, width, 3), dtype=np.uint8)
        
        # Update and draw objects
        for obj in objects:
            # Update position
            obj['pos'][0] += obj['vel'][0]
            obj['pos'][1] += obj['vel'][1]
            
            # Bounce off walls
            if obj['pos'][0] <= 0 or obj['pos'][0] + obj['size'][0] >= width:
                obj['vel'][0] *= -1
            if obj['pos'][1] <= 0 or obj['pos'][1] + obj['size'][1] >= height:
                obj['vel'][1] *= -1
            
            # Keep in bounds
            obj['pos'][0] = max(0, min(obj['pos'][0], width - obj['size'][0]))
            obj['pos'][1] = max(0, min(obj['pos'][1], height - obj['size'][1]))
            
            # Draw rectangle
            x, y = int(obj['pos'][0]), int(obj['pos'][1])
            w, h = obj['size']
            cv2.rectangle(frame, (x, y), (x + w, y + h), obj['color'], -1)
            
            # Add some noise to make it more realistic
            cv2.rectangle(frame, (x + 5, y + 5), (x + w - 5, y + h - 5), 
                         (255, 255, 255), 2)
        
        # Add frame number
        cv2.putText(frame, f"Frame: {frame_num}", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        out.write(frame)
    
    out.release()
    print("✓ Demo video created: demo_input.mp4")

def run_simple_tracking_demo():
    """Run a simple tracking demo using existing YOLO if available"""
    print("Running simple tracking demo...")
    
    try:
        # Try to import our system
        from deepsort_tracker.config import Config
        from deepsort_tracker.detector import YOLODetector, Detection
        from deepsort_tracker.utils import draw_tracks, draw_performance_info
        
        # Create config
        config = Config()
        config.detection.model_path = "yolov8n.pt"  # Will download if not exists
        config.detection.confidence_threshold = 0.3
        config.detection.device = "cpu"  # Use CPU to avoid CUDA issues
        
        print("✓ DeepSORT system imported successfully")
        
        # Check if we have a video to process
        video_files = [f for f in os.listdir('.') if f.endswith(('.mp4', '.avi', '.mov'))]
        
        if not video_files:
            print("No video files found. Creating demo video...")
            create_demo_video()
            video_file = "demo_input.mp4"
        else:
            video_file = video_files[0]
            print(f"Using video file: {video_file}")
        
        # Initialize detector (this might take time for first download)
        print("Initializing YOLO detector (this may take time for first download)...")
        detector = YOLODetector(config.detection)
        print("✓ Detector initialized")
        
        # Open video
        cap = cv2.VideoCapture(video_file)
        if not cap.isOpened():
            print(f"✗ Could not open video: {video_file}")
            return False
        
        # Get video properties
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        print(f"Video properties: {width}x{height}, {fps} FPS, {total_frames} frames")
        
        # Create output video
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter('demo_output.mp4', fourcc, fps, (width, height))
        
        frame_count = 0
        start_time = time.time()
        
        print("Processing video... Press 'q' to quit")
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            # Run detection
            detection_start = time.time()
            detections = detector.detect(frame)
            detection_time = time.time() - detection_start
            
            # Draw detections (simple visualization)
            for detection in detections:
                x1, y1, x2, y2 = map(int, detection.bbox)
                cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                cv2.putText(frame, f"Person: {detection.confidence:.2f}", 
                           (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
            
            # Calculate FPS
            elapsed = time.time() - start_time
            current_fps = frame_count / elapsed if elapsed > 0 else 0
            
            # Draw info
            cv2.putText(frame, f"Frame: {frame_count}/{total_frames}", (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            cv2.putText(frame, f"FPS: {current_fps:.1f}", (10, 60),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            cv2.putText(frame, f"Detections: {len(detections)}", (10, 90),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            cv2.putText(frame, f"Detection time: {detection_time*1000:.1f}ms", (10, 120),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            
            # Write frame
            out.write(frame)
            
            # Display frame (optional)
            cv2.imshow('DeepSORT Demo', frame)
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
            
            # Progress update
            if frame_count % 30 == 0:
                progress = (frame_count / total_frames) * 100
                print(f"Progress: {progress:.1f}% ({frame_count}/{total_frames})")
        
        # Cleanup
        cap.release()
        out.release()
        cv2.destroyAllWindows()
        detector.cleanup()
        
        # Final stats
        total_time = time.time() - start_time
        avg_fps = frame_count / total_time
        
        print(f"\n✓ Demo completed successfully!")
        print(f"  - Processed {frame_count} frames in {total_time:.1f} seconds")
        print(f"  - Average FPS: {avg_fps:.1f}")
        print(f"  - Output saved to: demo_output.mp4")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        print("Please install required dependencies:")
        print("  pip install ultralytics opencv-python torch")
        return False
    except Exception as e:
        print(f"✗ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_config_demo():
    """Run a simple configuration demo"""
    print("Running configuration demo...")
    
    try:
        from deepsort_tracker.config import Config
        
        # Create default config
        config = Config()
        print("✓ Default configuration created")
        
        # Show some settings
        print(f"  - YOLO model: {config.detection.model_path}")
        print(f"  - Confidence threshold: {config.detection.confidence_threshold}")
        print(f"  - Device: {config.detection.device}")
        print(f"  - Max age: {config.tracking.max_age}")
        print(f"  - Multiprocessing: {config.processing.enable_multiprocessing}")
        
        # Test environment variable override
        import os
        os.environ['CONFIDENCE_THRESHOLD'] = '0.7'
        os.environ['DETECTION_DEVICE'] = 'cpu'
        
        config_from_env = Config.from_env()
        print("✓ Configuration from environment created")
        print(f"  - Updated confidence: {config_from_env.detection.confidence_threshold}")
        print(f"  - Updated device: {config_from_env.detection.device}")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration demo failed: {e}")
        return False

def main():
    """Main demo function"""
    print("=" * 60)
    print("DeepSORT Tracking System Demo")
    print("=" * 60)
    
    print("\nSelect demo to run:")
    print("1. Configuration Demo (quick)")
    print("2. Simple Tracking Demo (requires model download)")
    print("3. Create Demo Video Only")
    print("0. Exit")
    
    while True:
        try:
            choice = input("\nEnter choice (0-3): ").strip()
            
            if choice == "0":
                print("Goodbye!")
                break
            elif choice == "1":
                run_config_demo()
            elif choice == "2":
                run_simple_tracking_demo()
            elif choice == "3":
                create_demo_video()
            else:
                print("Invalid choice. Please enter 0-3.")
                
        except KeyboardInterrupt:
            print("\nDemo interrupted. Goodbye!")
            break
        except Exception as e:
            print(f"Error: {e}")

if __name__ == "__main__":
    main()
