#!/usr/bin/env python3
"""
Simple test for the DeepSORT system without model downloads
"""
import numpy as np
import cv2
from deepsort_tracker.config import Config
from deepsort_tracker.detector import Detection
from deepsort_tracker.deep_sort.tracker import DeepSORTTracker
from deepsort_tracker.deep_sort.nn_matching import NearestNeighborDistanceMetric
from deepsort_tracker.deep_sort.detection import Detection as DeepSortDetection

def test_config():
    """Test configuration creation"""
    print("Testing configuration...")
    try:
        config = Config()
        print(f"✓ Config created successfully")
        print(f"  - Detection device: {config.detection.device}")
        print(f"  - Confidence threshold: {config.detection.confidence_threshold}")
        print(f"  - Max age: {config.tracking.max_age}")
        return True
    except Exception as e:
        print(f"✗ Config test failed: {e}")
        return False

def test_detection_class():
    """Test Detection class"""
    print("Testing Detection class...")
    try:
        detection = Detection([100, 100, 200, 300], 0.8)
        print(f"✓ Detection created: bbox={detection.bbox}, conf={detection.confidence}")
        print(f"  - Area: {detection.area}")
        print(f"  - Center: {detection.center}")
        return True
    except Exception as e:
        print(f"✗ Detection test failed: {e}")
        return False

def test_deepsort_detection():
    """Test DeepSORT Detection class"""
    print("Testing DeepSORT Detection class...")
    try:
        # Create detection in TLWH format
        detection = DeepSortDetection(
            tlwh=np.array([100, 100, 50, 100]),
            confidence=0.8,
            feature=np.random.randn(256).astype(np.float32)
        )
        print(f"✓ DeepSORT Detection created")
        print(f"  - TLWH: {detection.tlwh}")
        print(f"  - TLBR: {detection.to_tlbr()}")
        print(f"  - XYAH: {detection.to_xyah()}")
        print(f"  - Feature shape: {detection.feature.shape if detection.feature is not None else 'None'}")
        return True
    except Exception as e:
        print(f"✗ DeepSORT Detection test failed: {e}")
        return False

def test_tracker_creation():
    """Test tracker creation without running inference"""
    print("Testing tracker creation...")
    try:
        # Create metric
        metric = NearestNeighborDistanceMetric("cosine", 0.2, 100)
        print("✓ Metric created")
        
        # Create tracker
        tracker = DeepSORTTracker(metric, max_iou_distance=0.7, max_age=30, n_init=3)
        print("✓ Tracker created")
        
        # Test with dummy detections
        detections = []
        for i in range(3):
            detection = DeepSortDetection(
                tlwh=np.array([100 + i*50, 100, 50, 100]),
                confidence=0.8,
                feature=np.random.randn(256).astype(np.float32)
            )
            detections.append(detection)
        
        # Update tracker
        tracks = tracker.update(detections)
        print(f"✓ Tracker update successful, {len(tracks)} tracks created")
        
        # Test multiple updates
        for frame in range(3):
            moved_detections = []
            for i, detection in enumerate(detections):
                new_tlwh = detection.tlwh.copy()
                new_tlwh[0] += frame * 5  # Move right
                moved_detection = DeepSortDetection(
                    tlwh=new_tlwh,
                    confidence=0.8,
                    feature=np.random.randn(256).astype(np.float32)
                )
                moved_detections.append(moved_detection)
            
            tracks = tracker.update(moved_detections)
            print(f"  Frame {frame+1}: {len(tracks)} tracks")
        
        return True
    except Exception as e:
        print(f"✗ Tracker test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_monitor():
    """Test performance monitoring"""
    print("Testing performance monitor...")
    try:
        from deepsort_tracker.performance_monitor import PerformanceMonitor, Timer
        
        monitor = PerformanceMonitor()
        
        # Test timer
        with Timer("test_operation") as timer:
            import time
            time.sleep(0.01)  # 10ms
        
        print(f"✓ Timer test: {timer.get_elapsed_ms():.1f}ms")
        
        # Test monitor
        monitor.record_detection_time(0.015, 5)
        monitor.record_tracking_time(0.008, 3)
        monitor.record_total_time(0.025)
        
        metrics = monitor.get_current_metrics()
        print(f"✓ Performance monitor test successful")
        print(f"  - Detection time: {metrics.detection_time*1000:.1f}ms")
        print(f"  - Tracking time: {metrics.tracking_time*1000:.1f}ms")
        
        return True
    except Exception as e:
        print(f"✗ Performance monitor test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("=" * 50)
    print("DeepSORT System Simple Tests")
    print("=" * 50)
    
    tests = [
        ("Configuration", test_config),
        ("Detection Class", test_detection_class),
        ("DeepSORT Detection", test_deepsort_detection),
        ("Tracker Creation", test_tracker_creation),
        ("Performance Monitor", test_performance_monitor),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n--- {test_name} Test ---")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Print summary
    print("\n" + "=" * 50)
    print("Test Results Summary")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name:20} : {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        print("🎉 All tests passed! The DeepSORT system is working correctly.")
        print("\nNext steps:")
        print("1. Download YOLO models (will happen automatically on first use)")
        print("2. Download feature extraction model for person re-identification")
        print("3. Run: python3 run_deepsort.py track --video your_video.mp4")
    else:
        print("⚠ Some tests failed. Check the output above for details.")
    
    return passed == len(results)

if __name__ == "__main__":
    main()
