import json

def calculate_iou(bbox1, bbox2):
    """
    Calculate Intersection over Union (IoU) between two bounding boxes.
    Bounding boxes format: [x1, y1, x2, y2]
    """
    # Calculate coordinates of intersection
    x_left = max(bbox1[0], bbox2[0])
    y_top = max(bbox1[1], bbox2[1])
    x_right = min(bbox1[2], bbox2[2])
    y_bottom = min(bbox1[3], bbox2[3])
    
    # Check if there is any intersection
    if x_right < x_left or y_bottom < y_top:
        return 0.0
    
    # Calculate area of intersection
    intersection = (x_right - x_left) * (y_bottom - y_top)
    
    # Calculate areas of both bboxes
    area1 = (bbox1[2] - bbox1[0]) * (bbox1[3] - bbox1[1])
    area2 = (bbox2[2] - bbox2[0]) * (bbox2[3] - bbox2[1])
    
    # Calculate IoU
    union = area1 + area2 - intersection
    return intersection / union

def has_common_reid(reid1, reid2):
    """
    Check if two reid lists have any common elements.
    Returns the list of common elements if there are any, otherwise False.
    """
    common = list(set(reid1) & set(reid2))
    return common if common else False

def process_zone(zone_data):
    """
    Process a single zone's data to merge IDs based on IoU and reid.
    Returns detailed information about the merging decisions.
    """
    result = {
        "mergedGroups": [],
        "mergeDecisions": [],
        "totalCounter": 0
    }
    
    ids = list(zone_data.keys())
    processed_ids = set()
    
    # For each ID
    for i, id1 in enumerate(ids):
        if id1 in processed_ids:
            continue
            
        merged_group = {
            "ids": [id1],
            "bbox": zone_data[id1]["bbox"],
            "reid": zone_data[id1]["reid"].copy(),
            "counter": zone_data[id1]["counter"]
        }
        
        processed_ids.add(id1)
        
        # Find all IDs that should be merged with this one
        for j, id2 in enumerate(ids):
            if id1 == id2 or id2 in processed_ids:
                continue
                
            iou = calculate_iou(zone_data[id1]["bbox"], zone_data[id2]["bbox"])
            common_reids = has_common_reid(zone_data[id1]["reid"], zone_data[id2]["reid"])
            
            # Record the decision
            decision = {
                "id1": id1,
                "id2": id2,
                "iou": iou,
                "commonReids": common_reids,
                "merged": False,
                "reason": ""
            }
            
            # Check conditions for merging
            if iou > 0.9:
                # Merge based on IoU
                decision["merged"] = True
                decision["reason"] = "IoU > 0.9"
                
                merged_group["ids"].append(id2)
                merged_group["counter"] += zone_data[id2]["counter"]
                
                # Add new reids to the merged group
                for reid in zone_data[id2]["reid"]:
                    if reid not in merged_group["reid"]:
                        merged_group["reid"].append(reid)
                
                processed_ids.add(id2)
            elif common_reids:
                # Merge based on common reid
                decision["merged"] = True
                decision["reason"] = "Common reid"
                
                merged_group["ids"].append(id2)
                merged_group["counter"] += zone_data[id2]["counter"]
                
                # Add new reids to the merged group
                for reid in zone_data[id2]["reid"]:
                    if reid not in merged_group["reid"]:
                        merged_group["reid"].append(reid)
                
                processed_ids.add(id2)
            else:
                decision["reason"] = "No match (IoU <= 0.9 and no common reid)"
            
            result["mergeDecisions"].append(decision)
        
        result["mergedGroups"].append(merged_group)
        result["totalCounter"] += merged_group["counter"]
    
    return result

def process_data(data):
    """
    Process all zones in the data.
    Returns detailed results for each zone.
    """
    detailed_result = {}
    for zone in data:
        detailed_result[zone] = process_zone(data[zone])
    
    return detailed_result

def create_simplified_result(detailed_result):
    """
    Create a simplified view of the results, focusing on the merged groups 
    and total counters.
    """
    simplified_result = {}
    for zone in detailed_result:
        zone_data = detailed_result[zone]
        
        simplified_result[zone] = {
            "mergedGroups": [
                {
                    "ids": group["ids"],
                    "counter": group["counter"]
                } for group in zone_data["mergedGroups"]
            ],
            "totalCounter": zone_data["totalCounter"]
        }
    
    return simplified_result

def print_summary(simplified_result):
    """
    Print a human-readable summary of the aggregation results.
    """
    print("\nAGGREGATION SUMMARY:")
    print("--------------------")
    for zone in simplified_result:
        print(f"{zone}:")
        print(f"- Total Counter: {simplified_result[zone]['totalCounter']}")
        print(f"- Merged Groups: {len(simplified_result[zone]['mergedGroups'])}")
        
        for i, group in enumerate(simplified_result[zone]['mergedGroups']):
            print(f"  Group {i+1}: IDs [{', '.join(group['ids'])}], Counter: {group['counter']}")
        print()

def main(file_path):
    """
    Main function to process the JSON file and output the results.
    """
    # Read the JSON file
    with open(file_path, 'r') as f:
        data = json.load(f)
    
    # Process the data
    detailed_result = process_data(data)
    
    # Create simplified results
    simplified_result = create_simplified_result(detailed_result)
    
    # Output detailed results
    print("DETAILED RESULTS:")
    print(json.dumps(detailed_result, indent=2))
    
    # Output simplified results
    print("\nSIMPLIFIED RESULTS:")
    print(json.dumps(simplified_result, indent=2))
    
    # Print summary
    print_summary(simplified_result)
    
    return detailed_result, simplified_result

if __name__ == "__main__":
    # You can replace this with your file path
    file_path = "zones-04042025.json"
    detailed_result, simplified_result = main(file_path)
    with open('total-04042025.json','w') as f:
        f.write(json.dumps(detailed_result))