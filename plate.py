import torch
from inference import Inference
import torchvision.transforms as TF
from torchvision.io import read_image, ImageReadMode 
from torchvision.utils import draw_bounding_boxes, save_image

idx_2_char = ["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","P","Q","R","S","T","U","V","W","X","Y","Z",""]

class PlateNumber:
    def __init__(self) -> None:
        self.detector = Inference('weights/LPDNet_usa_pruned_tao5.onnx', outputs=[['batch',1,30,40],['batch',4,30,40]])
        self.recognizer = Inference('weights/us_lprnet_baseline18_deployable.onnx', inputs=[['batch',3,48,96]], outputs=[['batch',24],['batch',24]])
        self.detect_transform = TF.Compose(
            [
                TF.Resize((480,640)),
                TF.<PERSON>da(lambda x: (x/255).unsqueeze(0) )
            ]
        )
        self.recogn_transform = TF.Compose(
            [
                TF.Resize((48,96)),
                TF.Lambda(lambda x: (x/255).unsqueeze(0))
            ]
        )
        self.device = torch.device('cuda:0')

    def recognize(self, image: torch.Tensor):
        img = self.detect_transform(image).to(device=self.device)
        a = self.detector(img)
        print('text')

    def plate_number(self, image:torch.Tensor):
        img = self.recogn_transform(image).to(self.device)
        a = self.recognizer(img)
        print('text')

if __name__ == '__main__':
    i = read_image('test/plate.png', ImageReadMode.RGB)
    p = read_image('test/plate_test.png', ImageReadMode.RGB)
    a = PlateNumber()
    # a.recognize(i)
    a.plate_number(p)
    