from typing import Any, List, Literal, Dict, Tuple, Optional
import cv2
import numpy as np
import torch
import time
import os
import json
import signal
import threading
from datetime import datetime
from pathlib import Path
from uuid import uuid4

# OpenCV tracking imports
import cv2
import torch.multiprocessing as mp

# Torch imports
from torchvision.transforms import transforms as TF
import torchvision.transforms.functional as F
import torch.nn.functional as Fn
from torchvision.io import write_video
from torchvision.utils import draw_bounding_boxes, save_image

# Redis imports
import redis
from redis.commands.search.query import NumericFilter, Query
from redis.commands.search.indexDefinition import IndexDefinition, IndexType

# Project imports
from settings import env, LOGGER
from inference import Inference
from utils import (
    non_max_suppression, process_mask, scale_boxes, batch_probiou,
    pytorch_pure_iou, batch_bbox_polygon_iou_pure_pytorch,
    Object, _tlwh, _clip, calculate_iou
)
from cache import config, ReIDFeature, schema_reid

# Set up multiprocessing context
ctx = mp.get_context('spawn')


def letterbox_image(img, img_size=(640, 640), color=(127.5, 127.5, 127.5)):
    """
    Resize and pad image to fit the model input size while maintaining aspect ratio
    
    Args:
        img: Input image (numpy array or torch tensor)
        img_size: Target size (width, height)
        color: Padding color
        
    Returns:
        Resized and padded image
    """
    height = img_size[1]
    width = img_size[0]
    
    if isinstance(img, np.ndarray):
        shape = img.shape[:2]  # shape = [height, width]
        ratio = min(float(height)/shape[0], float(width)/shape[1])
        new_shape = (round(shape[1] * ratio), round(shape[0] * ratio))  # new_shape = [width, height]
        dw = (width - new_shape[0]) / 2  # width padding
        dh = (height - new_shape[1]) / 2  # height padding
        top, bottom = round(dh - 0.1), round(dh + 0.1)
        left, right = round(dw - 0.1), round(dw + 0.1)
        img = cv2.resize(img, new_shape, interpolation=cv2.INTER_AREA)  # resized, no border
        img = cv2.copyMakeBorder(img, top, bottom, left, right, cv2.BORDER_CONSTANT, value=color)  # padded rectangular
        return img
        
    if isinstance(img, torch.Tensor):
        shape = img.shape[1:]  # shape = [height, width]
        ratio = min(float(height)/shape[0], float(width)/shape[1])
        new_shape = (round(shape[0] * ratio), round(shape[1] * ratio))  # new_shape = [height, width]
        
        if 0 in new_shape or 1 in new_shape:
            return torch.zeros((3, img_size[0], img_size[1]))
            
        dw = (width - new_shape[1]) / 2  # width padding
        dh = (height - new_shape[0]) / 2  # height padding
        top, bottom = round(dh - 0.1), round(dh + 0.1)
        left, right = round(dw - 0.1), round(dw + 0.1)
        
        img = F.resize(img, new_shape)
        img = F.pad(img, (left, top, right, bottom), fill=127)
        return img

class ObjectProcess(ctx.Process):
    """
    Process class for person detection, tracking, and re-identification
    """
    def __init__(self,
                 content_queue: mp.Queue,
                 event: Any,
                 max_cosine_dist: float = 0.4,  # Reduced from 0.5 for better matching
                 nn_budget: int = 100,
                 max_age: int = 30,
                 device=torch.device('cuda:0' if torch.cuda.is_available() else 'cpu'),
                 output_dir: str = 'output'):
        super(ObjectProcess, self).__init__()
        
        # Queue and event for multiprocessing
        self.content_queue = content_queue
        self.stop_event = event
        
        # Initialize OpenCV Tracker
        LOGGER.info(f"Initializing OpenCV Tracker")
        self.trackers = []  # List to store individual trackers
        self.tracker_type = "MIL"  # Options: MIL, DaSiamRPN, GOTURN, Nano, Vit
        self.track_ids = {}  # Dictionary to store track IDs
        self.next_id = 1     # Counter for generating unique track IDs
        self.max_age = max_age  # Maximum frames to keep a track without updates
        self.tracks_age = {}  # Dictionary to track the age of each track
        self.tracked_boxes = []  # List to store current tracked boxes
        
        # Detection parameters
        self.image_size = (640, 640)  # YOLO input size
        self.conf = 0.4  # Confidence threshold
        self.iou = 0.5   # IoU threshold for NMS
        self.max_det = 300  # Maximum detections per image
        self.nc = 80  # Number of classes in COCO dataset
        
        # Device configuration
        self.device = device
        LOGGER.info(f"Using device: {self.device}")
        
        # Output directory
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
        # Load polygon settings if file exists
        self.polygon_settings = []
        self.zones = {}
        try:
            self.polygon_settings = self._load_config('./polygons.json')
            for i in self.polygon_settings:
                self.zones[i['label']] = {}
            LOGGER.info(f"Loaded polygon settings with {len(self.polygon_settings)} zones")
        except Exception as e:
            LOGGER.warning(f"Could not load polygon settings: {e}")
        
        # Video frame buffer for debugging
        self.video_frame = []
        
        # Re-ID parameters
        self.camera_id = env.camera_id if hasattr(env, 'camera_id') else 0
        self.dist_threshold = 0.3  # Similarity threshold for re-identification
        self.expired_secs = 86400  # 24 hours expiration for Redis entries
        
        # Statistics
        self.stats = {
            "processed_frames": 0,
            "detected_persons": 0,
            "tracked_persons": 0,
            "reid_matches": 0
        }

    def _load_config(self, path: str) -> List[Dict]:
        """Load configuration from JSON file"""
        try:
            with open(path) as f:
                return json.loads(f.read())
        except FileNotFoundError:
            LOGGER.warning(f"Config file {path} not found, using empty config")
            return []
        except json.JSONDecodeError:
            LOGGER.warning(f"Invalid JSON in {path}, using empty config")
            return []
    def initialize(self):
        """Initialize models and Redis connection"""
        # Initialize YOLO model for person detection
        LOGGER.info('Loading YOLO detection model')
        yolo_path = './weights/yolov8n.onnx'
        if not os.path.exists(yolo_path):
            LOGGER.error(f"YOLO model not found at {yolo_path}")
            raise FileNotFoundError(f"YOLO model not found at {yolo_path}")
        self.detector = Inference(yolo_path)
        
        # Initialize Swin Tiny model for feature extraction
        LOGGER.info('Loading Swin Tiny model for person re-identification')
        swin_path = './weights/swin_tiny_market1501_aicity156_featuredim256.onnx'
        if not os.path.exists(swin_path):
            LOGGER.error(f"Swin Tiny model not found at {swin_path}")
            raise FileNotFoundError(f"Swin Tiny model not found at {swin_path}")
        self.feature = Inference(swin_path)
        
        # Set up image transformations for detection
        self.detect_transform = TF.Compose([
            TF.Lambda(lambda x: letterbox_image(x, self.image_size).unsqueeze(0)/255)
        ])
        
        # Set up image transformations for feature extraction
        # Swin Tiny model expects 256x128 RGB images normalized with ImageNet stats
        self.feat_transform = TF.Compose([
            TF.Resize((256, 128)),
            TF.Lambda(lambda x: x/255),
            TF.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        # Initialize Redis connection
        LOGGER.info(f'Connecting to Redis at {config.redis_host}:{config.redis_port}')
        try:
            self.client = redis.Redis(
                host=config.redis_host,
                port=config.redis_port,
                decode_responses=True,
                socket_timeout=5.0
            )
            self.client.ping()  # Test connection
            LOGGER.info('Redis connection successful')
        except redis.ConnectionError as e:
            LOGGER.error(f"Redis connection failed: {e}")
            raise
        
        # Initialize Redis search index
        self.index = self.client.ft('idx:reid')
        try:
            self.index.info()
            LOGGER.info('Redis search index "idx:reid" found')
        except:
            LOGGER.warning('Index "idx:reid" not found - creating new index')
            definition = IndexDefinition(prefix=["reid:"], index_type=IndexType.JSON)
            self.index.create_index(fields=schema_reid, definition=definition)
            LOGGER.info('Created Redis search index "idx:reid"')

    def run(self):
        """Main process loop"""
        try:
            # Initialize models and connections
            self.initialize()
            
            # Create output directories
            results_dir = os.path.join(self.output_dir, 'results')
            os.makedirs(results_dir, exist_ok=True)
            
            if env.debug:
                debug_dir = os.path.join(self.output_dir, 'debug')
                os.makedirs(debug_dir, exist_ok=True)
            
            # Initialize tracking info
            start_time = time.time()
            info = []
            frame_count = 0
            
            # Main processing loop
            LOGGER.info("Starting person detection and tracking loop")
            last_check_time = time.time()
            last_save_time = time.time()
            
            while True:
                # Check for stop signal more frequently
                current_time = time.time()
                if current_time - last_check_time > 0.5:  # Check every 0.5 seconds
                    if self.stop_event.is_set():
                        LOGGER.info('Stop signal received, starting graceful shutdown')
                        break
                    last_check_time = current_time
                
                # Periodically save results even during processing
                # This ensures we don't lose data if the process is interrupted
                if current_time - last_save_time > 300:  # Save every 5 minutes
                    try:
                        LOGGER.info("Performing periodic save of results")
                        temp_results_file = os.path.join(results_dir, f'info-{env.date_str}-temp.json')
                        temp_zones_file = os.path.join(results_dir, f'zones-{env.date_str}-temp.json')
                        
                        with open(temp_results_file, 'w') as fw:
                            fw.write(json.dumps(info))
                            
                        with open(temp_zones_file, 'w') as fw:
                            fw.write(json.dumps(self.zones))
                            
                        LOGGER.info(f"Periodic save completed")
                        last_save_time = current_time
                    except Exception as e:
                        LOGGER.error(f"Error during periodic save: {e}")
                
                # Wait for new frame
                if self.content_queue.empty():
                    time.sleep(0.01)  # Small sleep to prevent CPU spinning
                    
                    # Check for stop event while waiting for frames
                    if self.stop_event.is_set():
                        LOGGER.info('Stop signal received while waiting for frames')
                        break
                        
                    continue
                
                # Get frame from queue
                counter, frame = self.content_queue.get()
                
                # Check for end of stream
                if frame is None:
                    LOGGER.info('End of stream detected')
                    self.stop_event.set()
                    break  # Exit the loop immediately instead of continuing
                
                # Process frame
                frame_count += 1
                self.stats["processed_frames"] += 1
                
                # Convert BGR to RGB and to tensor
                frame = frame[..., ::-1].copy()  # BGR to RGB
                H, W, _ = frame.shape
                frame_tensor = torch.tensor(frame).permute(2, 0, 1).to(self.device)
                
                # Detect persons in the frame
                objects: List[Object] = self.detect_object(frame_tensor)
                self.stats["detected_persons"] += len(objects)
                
                if len(objects) == 0:
                    continue
                
                # Extract features for each detected person
                embeddings: torch.Tensor = self.feature_extract(frame_tensor, objects)
                
                # Perform re-identification
                reid_results = self.search_similar(embeddings)
                
                # Update object IDs with re-identification results
                for idx, reid_id in reid_results:
                    objects[idx].id = reid_id
                    self.stats["reid_matches"] += 1
                
                # Convert frame tensor to numpy for OpenCV
                frame_np = frame_tensor.permute(1, 2, 0).cpu().numpy().astype(np.uint8)
                
                # Don't reset trackers periodically - this causes tracking discontinuities
                # Instead, rely on the track age mechanism to remove stale tracks
                # Only reset if the number of trackers gets too large to prevent memory issues
                if len(self.trackers) > 500:  # Only reset if we have too many trackers
                    LOGGER.warning(f"Too many trackers ({len(self.trackers)}), pruning oldest ones")
                    # Keep the 200 most recently updated trackers
                    active_trackers = sorted([(tid, age) for tid, age in self.tracks_age.items()], key=lambda x: x[1])[:200]
                    active_ids = [tid for tid, _ in active_trackers]
                    
                    # Filter trackers to keep only active ones
                    self.trackers = [t for i, t in enumerate(self.trackers) if list(self.track_ids.values())[i] in active_ids]
                    self.track_ids = {i: tid for i, (j, tid) in enumerate(zip(range(len(self.trackers)),
                                     [tid for tid in self.track_ids.values() if tid in active_ids]))}
                    self.tracks_age = {tid: age for tid, age in self.tracks_age.items() if tid in active_ids}
                    self.tracked_boxes = [box for i, box in enumerate(self.tracked_boxes) if i < len(self.trackers)]
                    
                    LOGGER.info(f"Pruned trackers, kept {len(self.trackers)} active ones")
                
                # Update existing trackers
                updated_boxes = []
                trackers_to_remove = []
                
                for i, (tracker, track_id) in enumerate(zip(self.trackers, self.track_ids.values())):
                    try:
                        success, box = tracker.update(frame_np)
                    except cv2.error as e:
                        LOGGER.error(f"Error updating tracker: {e}")
                        success = False
                        box = None
                    
                    if success:
                        updated_boxes.append(box)
                        self.tracks_age[track_id] = 0  # Reset age for successful tracks
                    else:
                        self.tracks_age[track_id] += 1
                        if self.tracks_age[track_id] > self.max_age:
                            trackers_to_remove.append(i)
                        updated_boxes.append(None)  # Placeholder for failed track
                
                # Remove trackers that haven't been updated for max_age frames
                for idx in sorted(trackers_to_remove, reverse=True):
                    track_id = list(self.track_ids.values())[idx]
                    del self.trackers[idx]
                    del self.tracks_age[track_id]
                    # Update track_ids dictionary
                    self.track_ids = {i: id for i, (j, id) in enumerate(zip(range(len(self.trackers)),
                                     [v for k, v in self.track_ids.items() if k != idx]))}
                
                # Store current tracked boxes
                self.tracked_boxes = [box for box in updated_boxes if box is not None]
                
                # Add new objects to tracker
                for obj in objects:
                    # Skip objects that are already being tracked
                    is_new = True
                    for box in self.tracked_boxes:
                        if box is not None:
                            # Convert from [x, y, width, height] to [x1, y1, x2, y2] for IOU calculation
                            x, y, w, h = box
                            tracked_bbox = [x, y, x+w, y+h]
                            if calculate_iou(obj.coord, tracked_bbox) > 0.5:
                                is_new = False
                                break
                    
                    if is_new:
                        # Create a new tracker for this object
                        if self.tracker_type == "MIL":
                            tracker = cv2.TrackerMIL_create()
                        elif self.tracker_type == "DaSiamRPN":
                            tracker = cv2.TrackerDaSiamRPN_create()
                        elif self.tracker_type == "GOTURN":
                            tracker = cv2.TrackerGOTURN_create()
                        elif self.tracker_type == "Nano":
                            tracker = cv2.TrackerNano_create()
                        elif self.tracker_type == "Vit":
                            tracker = cv2.TrackerVit_create()
                        else:
                            tracker = cv2.TrackerMIL_create()  # Default to MIL
                        
                        # Convert from [x1, y1, x2, y2] to [x, y, width, height]
                        x1, y1, x2, y2 = obj.coord
                        
                        # Ensure coordinates are valid (non-zero width and height)
                        if x2 <= x1 or y2 <= y1:
                            continue
                            
                        # OpenCV trackers expect (x, y, width, height) as a tuple of integers
                        bbox = (int(x1), int(y1), int(x2-x1), int(y2-y1))
                        
                        # Ensure the box is within the frame
                        if bbox[0] < 0 or bbox[1] < 0 or bbox[0] + bbox[2] >= frame_np.shape[1] or bbox[1] + bbox[3] >= frame_np.shape[0]:
                            continue
                            
                        # Ensure minimum size (10x10 pixels)
                        if bbox[2] < 10 or bbox[3] < 10:
                            continue
                        
                        # Initialize the tracker with the current frame
                        try:
                            success = tracker.init(frame_np, bbox)
                        except cv2.error as e:
                            LOGGER.error(f"Error initializing tracker: {e}")
                            success = False
                        
                        if success:
                            # Add to trackers list
                            self.trackers.append(tracker)
                            
                            # Assign a new ID to this track
                            track_idx = len(self.track_ids)
                            self.track_ids[track_idx] = self.next_id
                            self.tracks_age[self.next_id] = 0
                            self.next_id += 1
                            
                            # Add to tracked boxes
                            self.tracked_boxes.append(bbox)
                
                # Get tracking results
                bboxes = []
                track_ids = []
                
                for i, box in enumerate(self.tracked_boxes):
                    if i in self.track_ids and box is not None:
                        # Convert from (x, y, width, height) to [x1, y1, x2, y2]
                        x, y, w, h = box
                        bbox = [x, y, x+w, y+h]
                        bbox = _clip(bbox, frame_tensor.shape)
                        track_id = self.track_ids[i]
                    
                    bboxes.append(bbox)
                    track_ids.append(track_id)
                
                self.stats["tracked_persons"] += len(track_ids)
                
                if len(bboxes) == 0:
                    continue
                
                # Map tracking IDs to re-identification results
                objects = self._map_trackid_reid(objects, bboxes)
                
                # Count objects in polygons if defined
                if self.polygon_settings:
                    self._count_object_polygon(objects, track_ids, H, W)
                
                # Store detection and tracking results
                reid_ids = [obj.id for obj in objects]
                object_bboxes = [obj.coord for obj in objects]
                
                info.append({
                    'frame': counter,
                    'timestamp': int(time.time()),
                    'objects': [
                        {
                            'bbox': bbox,
                            'track_id': tid,
                            'reid_id': rid
                        }
                        for bbox, tid, rid in zip(object_bboxes, track_ids, reid_ids)
                    ]
                })
                
                # Generate debug visualization if enabled
                if env.debug:
                    self._debug_video(frame_tensor, objects, track_ids, counter, os.path.join(debug_dir, 'camera'))
                
                # Log progress periodically
                if frame_count % 100 == 0:
                    LOGGER.info(f"Processed {frame_count} frames, detected {self.stats['detected_persons']} persons, "
                                f"tracked {self.stats['tracked_persons']} persons, "
                                f"re-identified {self.stats['reid_matches']} persons")

        except Exception as err:
            LOGGER.exception(f"Error in processing loop: {err}")
            self.stop_event.set()
        
        # Always execute cleanup code, regardless of how we exited the loop
        try:
            # Save results
            results_file = os.path.join(results_dir, f'info-{env.date_str}.json')
            zones_file = os.path.join(results_dir, f'zones-{env.date_str}.json')
            
            LOGGER.info(f"Saving detection results to {results_file}")
            with open(results_file, 'w') as fw:
                fw.write(json.dumps(info))
                
            LOGGER.info(f"Saving zone statistics to {zones_file}")
            with open(zones_file, 'w') as fw:
                fw.write(json.dumps(self.zones))
                
            # Log statistics
            elapsed_time = time.time() - start_time
            LOGGER.info(f"Processing completed in {elapsed_time:.2f} seconds")
            LOGGER.info(f"Statistics: {self.stats}")
        except Exception as e:
            LOGGER.error(f"Error during cleanup: {e}")
        finally:
            # Always release resources
            self.release()

    def search_similar(self, embeddings: torch.Tensor) -> List[Tuple[int, str]]:
        """
        Search for similar embeddings in Redis and store new embeddings
        
        Args:
            embeddings: Tensor of person embeddings
            
        Returns:
            List of tuples (index, reid_id) for matched embeddings
        """
        matches = []
        pipe = self.client.pipeline()
        current_time = int(time.time())
        
        LOGGER.info(f"Searching for similar embeddings for {len(embeddings)} persons")
        
        # Log embedding statistics for debugging
        if len(embeddings) > 0:
            # Calculate embedding norms
            norms = torch.norm(embeddings, dim=1)
            LOGGER.debug(f"Embedding norms - min: {norms.min().item():.4f}, max: {norms.max().item():.4f}, mean: {norms.mean().item():.4f}")
            
            # Check if embeddings are properly normalized
            if not torch.allclose(norms, torch.ones_like(norms), rtol=1e-2):
                LOGGER.warning(f"Embeddings are not properly normalized. Norms should be close to 1.0")
        
        for idx, feat in enumerate(embeddings):
            # Convert embedding to bytes for Redis search
            embedding_bytes = feat.cpu().numpy().tobytes()
            
            # Search for similar embeddings using vector similarity search
            try:
                # KNN search with cosine similarity
                query_result = self.index.search(
                    Query(
                        '(*)=>[KNN 3 @feature $query_vector AS vector_score]'
                    ).sort_by('vector_score')
                     .return_fields('obj_id', 'vector_score', 'camera_id', 'timestamp')
                     .dialect(3),
                    {
                        'query_vector': embedding_bytes
                    }
                ).docs
                
                # Check if we found any matches
                if len(query_result) > 0:
                    best_match = query_result[0]
                    similarity_score = float(best_match.vector_score)
                    
                    # Log all potential matches for debugging
                    LOGGER.debug(f"Top match for idx={idx}: score={similarity_score:.4f} (threshold={self.dist_threshold})")
                    
                    # If similarity is below threshold, consider it a match
                    if similarity_score < self.dist_threshold:
                        try:
                            match_id = json.loads(best_match.obj_id)[0]
                            camera_id = json.loads(best_match.camera_id)[0]
                            timestamp = json.loads(best_match.timestamp)[0]
                            
                            # Only match if from different camera or sufficient time has passed
                            time_diff = current_time - timestamp
                            
                            # Cross-camera re-identification or same camera after time threshold
                            time_threshold = 60  # 60 seconds threshold
                            if (camera_id != self.camera_id) or (time_diff > time_threshold):
                                LOGGER.info(f"Found match: idx={idx}, id={match_id}, score={similarity_score:.4f}, "
                                           f"camera_id={camera_id}, time_diff={time_diff}s")
                                matches.append((idx, match_id))
                                continue
                            else:
                                LOGGER.debug(f"Skipping match (same camera, recent): idx={idx}, id={match_id}, "
                                           f"camera_id={camera_id}, time_diff={time_diff}s < {time_threshold}s")
                        except (json.JSONDecodeError, IndexError) as e:
                            LOGGER.error(f"Error parsing match data: {e}, raw data: {best_match}")
                    else:
                        LOGGER.debug(f"Match score {similarity_score:.4f} above threshold {self.dist_threshold}")
            
            except Exception as e:
                LOGGER.error(f"Error during vector search: {e}")
            
            # If no match found or error occurred, store the new embedding
            cache_id = str(uuid4())
            cache = ReIDFeature(
                obj_id=cache_id,
                feature=feat.tolist(),
                timestamp=current_time,
                camera_id=self.camera_id
            )
            
            # Add to Redis pipeline
            pipe.json().set(f'reid:{cache_id}', '$', cache.model_dump())
            
            # Set expiration time
            pipe.expire(f'reid:{cache_id}', self.expired_secs)
        
        # Execute all Redis commands in the pipeline with retry logic
        max_retries = 3
        retry_count = 0
        success = False
        
        while not success and retry_count < max_retries:
            try:
                pipe.execute()
                success = True
                LOGGER.debug(f"Successfully stored {len(embeddings)} new embeddings in Redis")
            except redis.ConnectionError as e:
                retry_count += 1
                LOGGER.warning(f"Redis connection error (attempt {retry_count}/{max_retries}): {e}")
                if retry_count < max_retries:
                    time.sleep(1)  # Wait before retrying
                else:
                    LOGGER.error(f"Failed to store embeddings in Redis after {max_retries} attempts")
            except Exception as e:
                LOGGER.error(f"Error executing Redis pipeline: {e}")
                break  # Don't retry for non-connection errors
        
        LOGGER.info(f"Found {len(matches)} matches out of {len(embeddings)} persons")
        
        # Log match rate
        if len(embeddings) > 0:
            match_rate = len(matches) / len(embeddings) * 100
            LOGGER.info(f"Match rate: {match_rate:.1f}%")
            
            # Check if match rate is suspiciously low
            if match_rate < 10 and len(embeddings) > 5:
                LOGGER.warning(f"Low match rate ({match_rate:.1f}%) might indicate re-ID issues")
        
        return matches

    def _map_trackid_reid(self, objects: List[Object], bboxes: List[torch.Tensor]) -> List[Object]:
        """
        Map detected objects to tracked bounding boxes
        
        Args:
            objects: List of detected objects with features
            bboxes: List of tracked bounding boxes
            
        Returns:
            List of objects mapped to tracking IDs
        """
        if not objects or not bboxes:
            LOGGER.debug(f"No objects or bboxes to map: objects={len(objects) if objects else 0}, bboxes={len(bboxes) if bboxes else 0}")
            return objects
            
        # Log input sizes
        LOGGER.debug(f"Mapping {len(objects)} detected objects to {len(bboxes)} tracked boxes")
        
        # Convert detection bboxes to tensor
        reID_bbox = torch.stack([torch.tensor(obj.coord) for obj in objects])
        reID_bbox = torch.concat([reID_bbox, torch.ones(len(reID_bbox), 1)], dim=1).to(self.device)  # tlbr-rate
        
        # Convert tracking bboxes to tensor
        track_bbox_pre = torch.concat([torch.stack(bboxes), torch.ones(len(bboxes), 1).to(self.device)], dim=1)
        
        # Calculate IoU between detection and tracking bboxes
        post_bboxes = batch_probiou(reID_bbox, track_bbox_pre)
        
        # Log IoU matrix statistics
        max_ious, _ = post_bboxes.max(dim=0)
        LOGGER.debug(f"IoU matrix shape: {post_bboxes.shape}, Max IoUs: min={max_ious.min().item():.3f}, max={max_ious.max().item():.3f}, mean={max_ious.mean().item():.3f}")
        
        # Match each tracking box to the detection with highest IoU
        # Lower threshold from 0.8 to 0.5 for better matching
        match_threshold = 0.5  # Reduced from 0.8
        matches = post_bboxes > match_threshold
        match_count = matches.sum().item()
        LOGGER.debug(f"IoU matching: found {match_count} matches above threshold {match_threshold}")
        
        ind = matches.int().argmax(0).cpu().tolist()
        
        # Check for duplicate assignments (same detection assigned to multiple tracks)
        unique_inds = set(ind)
        if len(unique_inds) < len(ind):
            LOGGER.warning(f"Duplicate assignments detected: {len(ind) - len(unique_inds)} tracks assigned to the same detection")
            
            # Count occurrences of each index
            from collections import Counter
            ind_counts = Counter(ind)
            duplicates = {idx: count for idx, count in ind_counts.items() if count > 1}
            LOGGER.debug(f"Duplicate indices: {duplicates}")
        
        # Log matching results
        LOGGER.debug(f"Matched {len(ind)} tracked boxes to {len(unique_inds)} unique detections")
        
        # Return matched objects
        matched_objects = [objects[i] for i in ind]
        
        # Log reid IDs for debugging
        reid_ids = [obj.id for obj in matched_objects if obj.id is not None]
        LOGGER.debug(f"Matched objects have {len(reid_ids)} reid IDs out of {len(matched_objects)} objects")
        
        return matched_objects
    
    def _count_object_polygon(self, objs: List[Object], track_ids: List[int], H: int, W: int):
        """
        Count objects in defined polygon zones
        
        Args:
            objs: List of detected and tracked objects
            track_ids: List of tracking IDs
            H: Image height
            W: Image width
        """
        if not objs or not track_ids or not self.polygon_settings:
            return
            
        # Extract bounding boxes
        reid_bboxes = [obj.coord for obj in objs]
        reid_bboxes = torch.tensor(reid_bboxes)
        
        # Process each polygon zone
        for poly in self.polygon_settings:
            # Calculate IoU between bboxes and polygon
            ious_pytorch = batch_bbox_polygon_iou_pure_pytorch(reid_bboxes, poly['points'], (H, W))
            
            # Find objects with IoU > 0.3 (inside or significantly overlapping with polygon)
            ind = torch.where(ious_pytorch > 0.3)[0]
            
            # Get track IDs, bboxes and re-ID IDs for objects in polygon
            inter_track_ids = torch.tensor(track_ids)[ind].tolist()
            inter_bboxes = reid_bboxes[ind].cpu().tolist()
            inter_reid = [objs[k].id for k in ind]
            
            # Update zone statistics
            for tid, bb, rid in zip(inter_track_ids, inter_bboxes, inter_reid):
                if self.zones[poly['label']].get(tid, None) is None:
                    # New object in zone
                    self.zones[poly['label']][tid] = {
                        'bbox': bb,
                        'reid': [rid] if rid is not None else [],
                        'counter': 1
                    }
                else:
                    # Existing object in zone
                    self.zones[poly['label']][tid]['counter'] += 1
                    if rid is not None and rid not in self.zones[poly['label']][tid]['reid']:
                        self.zones[poly['label']][tid]['reid'].append(rid)

    def _debug_video(self, frame: torch.Tensor, objs: List[Object], track_ids: List[int],
                    count: int, output_path: str = 'test/camera'):
        """
        Generate debug visualization video with bounding boxes and tracking/re-ID information
        
        Args:
            frame: Input frame tensor
            objs: List of detected and tracked objects
            track_ids: List of tracking IDs
            count: Frame counter
            output_path: Directory to save debug videos
        """
        # Ensure output directory exists
        os.makedirs(output_path, exist_ok=True)
        
        # Extract re-ID IDs and bounding boxes
        reid_ids = [obj.id for obj in objs if obj.id is not None]
        reid_bboxes = [obj.coord for obj in objs]
        
        # Skip if no detections
        if not reid_bboxes:
            return
            
        # Convert bounding boxes to tensor
        reid_bboxes = torch.tensor(reid_bboxes)
        
        # Check if IDs match
        if len(reid_ids) != len(track_ids):
            LOGGER.warning(f"Re-ID IDs count ({len(reid_ids)}) doesn't match track IDs count ({len(track_ids)})")
            return
        
        # Create labels for visualization
        labels = [f'{tid}-{rid[:6] if rid else "None"}' for tid, rid in zip(track_ids, reid_ids)]
        
        # Draw bounding boxes on frame
        img = draw_bounding_boxes(
            frame,
            boxes=reid_bboxes,
            labels=labels,
            colors="red",
            width=2,
            font_size=16
        )
        
        # Add frame to video buffer
        self.video_frame.append(img)
        
        # Write video when buffer is full
        if len(self.video_frame) > 300:
            LOGGER.debug(f"Writing debug video to {output_path}/{count}.mp4")
            
            # Stack frames and write video
            frames = torch.stack(self.video_frame)
            self.video_frame = []
            
            try:
                # Write video file
                write_video(f'{output_path}/{count}.mp4', frames.permute(0, 2, 3, 1), fps=15)
                
                # Write zone statistics
                with open(f'{output_path}/{count}.json', 'w') as fw:
                    fw.write(json.dumps(self.zones))
                    
                LOGGER.debug(f"Debug video written successfully")
            except Exception as e:
                LOGGER.error(f"Error writing debug video: {e}")

        
    def detect_object(self, img: torch.Tensor) -> List[Object]:
        """
        Detect persons in an image using YOLO
        
        Args:
            img: Input image tensor (C, H, W)
            
        Returns:
            List of detected person objects
        """
        # Check for stop signal before starting detection
        if self.stop_event.is_set():
            LOGGER.info("Stop event detected before starting detection")
            return []
            
        C, H, W = img.shape
        org_size = (H, W)
        
        # Preprocess image for YOLO
        input_img = self.detect_transform(img)
        
        # Run YOLO inference with interrupt checking
        try:
            detector_output = self.detector(input_img)
        except KeyboardInterrupt:
            LOGGER.info("KeyboardInterrupt during detection, setting stop event")
            self.stop_event.set()
            return []
        except Exception as e:
            LOGGER.error(f"Error during detection: {e}")
            return []
            
        # Check for stop signal after detection
        if self.stop_event.is_set():
            LOGGER.info("Stop event detected after detection")
            return []
        
        # Add detailed logging
        if isinstance(detector_output, list):
            LOGGER.info(f"Detector returned a list with {len(detector_output)} elements")
        else:
            LOGGER.info(f"Detector returned a single tensor of shape {detector_output.shape}")
        
        # Handle both single output and dual output cases
        # YOLOv8-seg models return (pred, proto), while standard YOLOv8 models return just pred
        if isinstance(detector_output, list) and len(detector_output) > 1:
            LOGGER.info("Using dual-output mode (pred, proto)")
            pred, proto = detector_output  # Unpack for YOLOv8-seg models
        else:
            LOGGER.info("Using single-output mode (pred only)")
            if isinstance(detector_output, list):
                pred = detector_output[0]  # Extract first element if it's a list
            else:
                pred = detector_output  # Use directly if it's already a tensor
            proto = None  # Not used in this method anyway
        
        # Apply non-max suppression to get person detections (class 0)
        p = non_max_suppression(
            pred,
            self.conf,
            self.iou,
            agnostic=False,
            max_det=self.max_det,
            nc=self.nc,
            classes=[0],  # Only detect persons (class 0 in COCO)
        )
        
        # Process detections
        bboxes = []
        conf = []
        
        for i, pred in enumerate(p):
            # Scale boxes back to original image size
            pred[:, :4] = scale_boxes(self.image_size, pred[:, :4], org_size)
            bboxes.append(pred[:, :4])
            conf.append(pred[:, 4])
        
        # If no detections, return empty list
        if not bboxes:
            return []
            
        # Convert to numpy arrays
        bboxes = torch.concat(bboxes).to(dtype=torch.int32).cpu().numpy().tolist()
        conf = torch.concat(conf).cpu().numpy().tolist()
        
        # Create Object instances for each detection
        objects = [Object(coord=bbox, conf=c, mode='xyxy') for bbox, c in zip(bboxes, conf)]
        
        return objects

    def feature_extract(self, img: torch.Tensor, objects: List[Object]) -> torch.Tensor:
        """
        Extract feature embeddings for detected persons using Swin Tiny model
        
        Args:
            img: Input image tensor
            objects: List of detected person objects
            
        Returns:
            Tensor of feature embeddings for each person
        """
        # Check for stop signal before starting feature extraction
        if self.stop_event.is_set() or not objects:
            LOGGER.info("Stop event detected before feature extraction or no objects")
            return torch.zeros((len(objects) if objects else 0, 256), device=self.device)
            
        # Crop person regions and apply transformations
        try:
            # Stack all person crops into a batch
            input_objects = torch.stack([self.feat_transform(obj.crop(img)) for obj in objects])
            
            # Check for stop signal before running the model
            if self.stop_event.is_set():
                LOGGER.info("Stop event detected before feature model inference")
                return torch.zeros((len(objects), 256), device=self.device)
                
            # Extract features using Swin Tiny model
            try:
                features = self.feature(input_objects)[0]
            except KeyboardInterrupt:
                LOGGER.info("KeyboardInterrupt during feature extraction, setting stop event")
                self.stop_event.set()
                return torch.zeros((len(objects), 256), device=self.device)
            
            # Check for stop signal after feature extraction
            if self.stop_event.is_set():
                LOGGER.info("Stop event detected after feature extraction")
                return torch.zeros((len(objects), 256), device=self.device)
                
            # Normalize features (L2 normalization)
            features = F.normalize(features, p=2, dim=1)
            
            return features
        except Exception as e:
            LOGGER.error(f"Error extracting features: {e}")
            # Return empty features in case of error
            return torch.zeros((len(objects), 256), device=self.device)
    
    def release(self):
        """Release resources and clean up"""
        self.stop_event.set()
        LOGGER.info('Shutting down person detection and tracking process')
        
        # Close Redis connection if it exists
        if hasattr(self, 'client') and self.client:
            try:
                self.client.close()
                LOGGER.info("Redis connection closed")
            except Exception as e:
                LOGGER.error(f"Error closing Redis connection: {e}")
        
        # Release ONNX resources
        if hasattr(self, 'detector'):
            del self.detector
        if hasattr(self, 'feature'):
            del self.feature
            
        # Clear any large data structures
        if hasattr(self, 'video_frame'):
            self.video_frame.clear()
        
        # Log final statistics
        LOGGER.info(f"Final statistics: {self.stats}")
        LOGGER.info("All resources released")


# if __name__ == "__main__":
#     mp.set_start_method('spawn')
#     q = mp.Queue(100)
#     c = mp.Queue(1)
#     proc = PersonProcess('rtmp://192.168.0.162/static/loop',q, c)
#     proc.start()
#     while(True):
#         pass
#     print('TEST')