from typing import Any, Dict, List, Union
from uuid import uuid4
import onnxruntime
import torch
import numpy as np

def safe_str2int(a: str):
    try:
        return int(a)
    except:
        return None

class Dim:
    def __init__(self, name, shape, type) -> None:
        self.name = name
        self.shape = shape
        self.type = type

import signal
import threading
import time

# Global flag to indicate if inference should be interrupted
inference_interrupt_flag = False

class InferenceTimeoutError(Exception):
    """Exception raised when inference takes too long"""
    pass

class Inference:
    def __init__(self, path_onnx, inputs: Any = None, outputs: Any = None, num_thread: int = 8, device='cuda:0', timeout=30) -> None:
        if (device.find('cuda') > -1) and ('CUDAExecutionProvider' in onnxruntime.get_available_providers()):
            provider_device = [('CUDAExecutionProvider', {
                'device_id': 0,
                'arena_extend_strategy': 'kNextPowerOfTwo',
                'gpu_mem_limit': 2 * 1024 * 1024 * 1024,
                'cudnn_conv_algo_search': 'EXHAUSTIVE',
                'do_copy_in_default_stream': True,
            }),
            ]
        else:
            provider_device = ['CPUExecutionProvider']
            # LOGGING.error('GPU not available!!')
        self.device = torch.device(device)
        sess_options = onnxruntime.SessionOptions()
        sess_options.intra_op_num_threads = num_thread
        sess_options.execution_mode = onnxruntime.ExecutionMode.ORT_PARALLEL
        # sess_options.graph_optimization_level = onnxruntime.GraphOptimizationLevel.ORT_ENABLE_ALL
        
        # Set timeout for inference operations
        self.timeout = timeout  # seconds
        
        # Load the model with a timeout
        try:
            self.model = onnxruntime.InferenceSession(
                path_onnx, sess_options=sess_options, providers=provider_device)
        except Exception as e:
            print(f"Error loading model: {e}")
            raise

        self.input_shape = self.set_dim('inputs',inputs)
        self.output_shape = self.set_dim('outputs',outputs)
        self.str_type = {'tensor(float)': 'float32',
                         'tensor(float16)': 'float16',
                         'tensor(int64)':'int64',
                         'tensor(int32)':'int32'}
        self.map_nptype = {'float16': np.float16, 'float32': np.float32, 'int64': np.int64, 'int32': np.int32}
        self.map_dtype = {'float16': torch.float16, 'float32': torch.float32, 'int64': torch.int64, 'int32':torch.int32}

    def __call__(self, *args) -> list[torch.Tensor]:
        """Call the model and return outputs.
        
        Note: For YOLOv8-seg models, this should return two values (pred, proto),
        but for other models it may return just one tensor.
        """
        binding = self.model.io_binding()
        assert len(args) == len(self.input_shape), 'amount of inputs must be equal than amount of model\'s input'
        for i, x in enumerate(args):
            # if self.model.get_inputs())
            X_tensor = x.contiguous()
            x_name = self.input_shape[i].name
            x_type = self.str_type[self.input_shape[i].type]
            x_shape = self.input_shape[i].shape

            assert x_type == str(X_tensor.dtype).split(
                '.')[1], f'input\'s type incorrect. Expect: {x_type}'
            assert [safe_str2int(k) for k in x_shape[1:]] == list(X_tensor.shape)[
                1:], f'input\'s shape incorrect. Expect: {x_shape}'

            if X_tensor.data_ptr() == 0:
                X_empty = torch.zeros(
                    1, *list(X_tensor.shape)[1:], dtype=X_tensor.dtype, device=self.device).contiguous()
                binding.bind_input(
                    name=x_name,
                    device_type=X_empty.device.type,
                    device_id=X_empty.device.index,
                    element_type=self.map_nptype[x_type],
                    shape=tuple(X_empty.shape),
                    buffer_ptr=X_empty.data_ptr(),
                )
                continue

            binding.bind_input(
                name=x_name,
                device_type=X_tensor.device.type,
                device_id=X_tensor.device.index,
                element_type=self.map_nptype[x_type],
                shape=tuple(X_tensor.shape),
                buffer_ptr=X_tensor.data_ptr(),
            )
        # Allocate the PyTorch tensor for the model output
        outputs = []
        for out in self.output_shape:
            output_name = out.name
            output_shape = out.shape.copy()
            output_type = self.str_type[out.type]

            if output_shape[0] != 'batch_size' and output_shape[0] != 'batch' and safe_str2int(output_shape[0]) is not None:
                output_shape[0] = int(output_shape[0])
            elif output_shape[0] == 'batch_size' or output_shape[0] == 'batch':
                output_shape[0] = args[0].shape[0]
            else:
                # find exactly dynamic axes
                for i, k in enumerate(args):
                    dynamic_shape = self.model.get_inputs()[i].shape
                    if dynamic_shape[0] == output_shape[0]:
                        if k.shape[0] == 0:
                            output_shape[0] = 1
                            break
                        output_shape[0] = k.shape[0]  # 'batch_size' -> 1
                        break
            output_shape = [int(osh) for osh in output_shape]
            output = torch.empty(
                output_shape, dtype=self.map_dtype[output_type], device=self.device).contiguous()
            outputs.append(output)
            binding.bind_output(
                name=output_name,
                device_type=output.device.type,
                device_id=output.device.index,
                element_type=self.map_nptype[output_type],
                shape=tuple(output.shape),
                buffer_ptr=output.data_ptr(),
            )
        # Function to run inference with timeout
        def run_inference_with_timeout(binding, timeout):
            global inference_interrupt_flag
            inference_interrupt_flag = False
            result = {"success": False, "error": None}
            
            def target_function():
                try:
                    if not inference_interrupt_flag:
                        self.model.run_with_iobinding(binding)
                        result["success"] = True
                except Exception as e:
                    result["error"] = e
            
            thread = threading.Thread(target=target_function)
            thread.daemon = True  # Daemon threads are killed when the main program exits
            
            thread.start()
            thread.join(timeout)
            
            if thread.is_alive():
                # If thread is still running after timeout, set interrupt flag
                inference_interrupt_flag = True
                # Return timeout error
                return False, InferenceTimeoutError(f"Inference timed out after {timeout} seconds")
            
            if not result["success"]:
                return False, result["error"]
            
            return True, None
        
        try:
            # Run inference with timeout
            success, error = run_inference_with_timeout(binding, self.timeout)
            
            if not success:
                # Log the error and return empty outputs
                print(f"ERROR in inference: {error}")
                # Return empty tensors with the expected shapes
                empty_outputs = []
                for out in self.output_shape:
                    output_shape = out.shape.copy()
                    output_type = self.str_type[out.type]
                    
                    # Handle dynamic shapes
                    if output_shape[0] != 'batch_size' and output_shape[0] != 'batch' and safe_str2int(output_shape[0]) is not None:
                        output_shape[0] = int(output_shape[0])
                    else:
                        output_shape[0] = 1  # Default to batch size 1 for errors
                    
                    output_shape = [int(osh) for osh in output_shape]
                    empty_output = torch.zeros(
                        output_shape, dtype=self.map_dtype[output_type], device=self.device).contiguous()
                    empty_outputs.append(empty_output)
                
                return empty_outputs
            
            # Use proper logging instead of print statements
            # print(f"DEBUG: Inference.__call__ returning {len(outputs)} outputs")
            
            # Always return the list of outputs
            # The calling code should handle unpacking appropriately
            return outputs
            
        except KeyboardInterrupt:
            print("Keyboard interrupt detected during inference, returning empty outputs")
            # Return empty tensors with the expected shapes
            empty_outputs = []
            for out in self.output_shape:
                output_shape = out.shape.copy()
                output_type = self.str_type[out.type]
                
                # Handle dynamic shapes
                if output_shape[0] != 'batch_size' and output_shape[0] != 'batch' and safe_str2int(output_shape[0]) is not None:
                    output_shape[0] = int(output_shape[0])
                else:
                    output_shape[0] = 1  # Default to batch size 1 for errors
                
                output_shape = [int(osh) for osh in output_shape]
                empty_output = torch.zeros(
                    output_shape, dtype=self.map_dtype[output_type], device=self.device).contiguous()
                empty_outputs.append(empty_output)
            
            # Set global interrupt flag
            global inference_interrupt_flag
            inference_interrupt_flag = True
            
            return empty_outputs
            
        except Exception as e:
            # Log the error and return empty outputs to prevent crashes
            print(f"ERROR in inference: {e}")
            # Return empty tensors with the expected shapes
            empty_outputs = []
            for out in self.output_shape:
                output_shape = out.shape.copy()
                output_type = self.str_type[out.type]
                
                # Handle dynamic shapes
                if output_shape[0] != 'batch_size' and output_shape[0] != 'batch' and safe_str2int(output_shape[0]) is not None:
                    output_shape[0] = int(output_shape[0])
                else:
                    output_shape[0] = 1  # Default to batch size 1 for errors
                
                output_shape = [int(osh) for osh in output_shape]
                empty_output = torch.zeros(
                    output_shape, dtype=self.map_dtype[output_type], device=self.device).contiguous()
                empty_outputs.append(empty_output)
            
            return empty_outputs

    def set_dim(self, type_io:str,dim=None):
        _dim = getattr(self.model,f'get_{type_io}')()
        if dim is None:
            for i in _dim:
                logic = [isinstance(j, str) for j in i.shape[1:]]
                if all(logic):
                    raise f'{type_io} must be set'
            _io = [Dim(name=i.name,shape=i.shape,type=i.type) for i in _dim]
        else:
            _io = [Dim(name=i.name,shape=j,type=i.type) for i,j in zip(_dim,dim)]
        return _io