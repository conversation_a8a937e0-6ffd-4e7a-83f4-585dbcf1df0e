from typing import List
from pydantic_settings import BaseSettings
from pydantic import BaseModel, model_serializer, Field
import torch
import logging
from uuid import uuid4
from pymilvus import FieldSchema, CollectionSchema, DataType

logFormatter = logging.Formatter(fmt='%(asctime)s - %(filename)s - %(funcName)s - %(lineno)d - [%(levelname)s] : %(message)s')
LOGGER = logging.Logger("consumer-sd")
handler = logging.StreamHandler()
handler.setFormatter(logFormatter)
LOGGER.addHandler(handler)

class Settings(BaseSettings):
    output: str = 'output/ai-data.json'
    max_queue: int = 200
    acion_threshhold: float = 0.6
    content_id: str | None = None
    season_id: str | None = None
    video_url: str | None = None
    clip_host: str = 'grpc://*************:51000'
    weaviate_host: str = 'localhost:31559'
    index_obj: str = 'smart_city_objects'
    index_camera: str = 'smart_city_camera'
    index_description: str = 'smart_city_description'
    ollama_host: str = 'http://localhost:11434'
    ollama_gen_endpoint:str = '/api/generate' 
    skip_frame: int = 12
    debug: bool = True
    date_str: str = '03042025'

env = Settings()


class ImageElement():
    def __init__(self, image: torch.Tensor, bbox: list[int], index:int, class_id:int):
        self.image = image
        self.bbox = bbox
        self.class_id = class_id
        self.index = index
    
    def update(self, image: torch.Tensor, bbox:list[int],index: int):
        self.image = torch.concat([self.image, image])
        self.index = index

    @property
    def size(self):
        return len(self.image)


class SmartCity(BaseModel):
    timestamp: int
    cameraid: int
    vector: list[float]


class OllamaBody(BaseModel):
    model: str = 'llava:13b'
    prompt: str
    images: List[str] | None = None

id_field = FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, description="primary id")
timestamp_field = FieldSchema(name="timestamp", dtype=DataType.INT64, description="timestamp when image appear on camera_id")
camera_id_field = FieldSchema(name="camera_id", dtype=DataType.INT64, description="camera_id")
embedding_field = FieldSchema(name="vector", dtype=DataType.FLOAT_VECTOR, dim=768, description="vector")

schema = CollectionSchema(fields=[id_field,timestamp_field,camera_id_field,embedding_field], auto_id=True, enable_dynamic_field=True)
