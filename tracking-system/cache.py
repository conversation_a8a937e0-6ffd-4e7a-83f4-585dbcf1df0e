from typing import List
import redis
from redis.commands.search.field import (
    NumericField,
    TagField,
    TextField,
    VectorField,
    
)
from pydantic import BaseModel
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    redis_host: str = '***************'
    redis_port: int = 31876

config = Settings()

class Maskmem(BaseModel):
    camera_id: int
    maskmem_feat: List[float]
    maskmem_pos: List[float]
    obj_ptr: List[float]
    maskmem_size: List[int] = [1,64,64,64]
    obj_ptr_size: List[int] = [1,256]
    timestamp: int

schema = (
    NumericField('$.timestamp', as_name='timestamp'),
    NumericField('$.camera_id', as_name='camera_id')
    
)

class CLIPFeatures(BaseModel):
    feature: List[float]
    obj_id: int
    timestamp: int

schema_clip_model = (
    NumericField('$.timestamp', as_name='timestamp'),
    NumericField('$.obj_id', as_name='obj_id'),
    VectorField(
        "$.feature",
        "FLAT",
        {
            "TYPE": "FLOAT32",
            "DIM": 768,
            "DISTANCE_METRIC": "COSINE",
        },
        as_name="feature",
    )
)

class ReIDFeature(BaseModel):
    feature: List[float]
    obj_id: str
    timestamp: int
    camera_id: int


schema_reid = (
    NumericField('$.timestamp', as_name='timestamp'),
    NumericField('$.camera_id', as_name='camera_id'),
    TextField('$.obj_id', as_name='obj_id'),
    VectorField(
        "$.feature",
        "FLAT",
        {
            "TYPE": "FLOAT32",
            "DIM": 256,
            "DISTANCE_METRIC": "COSINE",
        },
        as_name="feature",
    )
)