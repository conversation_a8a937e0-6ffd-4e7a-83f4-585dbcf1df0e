from typing import Literal
import torch
import torchvision
import numpy as np
import torch.nn.functional as F
from PIL import Image
import io
import base64
from shapely.geometry import Polygon, box

def crop_mask(masks, boxes):
    """
    It takes a mask and a bounding box, and returns a mask that is cropped to the bounding box.

    Args:
        masks (torch.Tensor): [n, h, w] tensor of masks
        boxes (torch.Tensor): [n, 4] tensor of bbox coordinates in relative point form

    Returns:
        (torch.Tensor): The masks are being cropped to the bounding box.
    """
    _, h, w = masks.shape
    x1, y1, x2, y2 = torch.chunk(boxes[:, :, None], 4, 1)  # x1 shape(n,1,1)
    r = torch.arange(w, device=masks.device, dtype=x1.dtype)[None, None, :]  # rows shape(1,1,w)
    c = torch.arange(h, device=masks.device, dtype=x1.dtype)[None, :, None]  # cols shape(1,h,1)

    return masks * ((r >= x1) * (r < x2) * (c >= y1) * (c < y2))

def clip_boxes(boxes, shape):
    """
    Takes a list of bounding boxes and a shape (height, width) and clips the bounding boxes to the shape.

    Args:
        boxes (torch.Tensor): the bounding boxes to clip
        shape (tuple): the shape of the image

    Returns:
        (torch.Tensor | numpy.ndarray): Clipped boxes
    """
    if isinstance(boxes, torch.Tensor):  # faster individually (WARNING: inplace .clamp_() Apple MPS bug)
        boxes[..., 0] = boxes[..., 0].clamp(0, shape[1])  # x1
        boxes[..., 1] = boxes[..., 1].clamp(0, shape[0])  # y1
        boxes[..., 2] = boxes[..., 2].clamp(0, shape[1])  # x2
        boxes[..., 3] = boxes[..., 3].clamp(0, shape[0])  # y2
    else:  # np.array (faster grouped)
        boxes[..., [0, 2]] = boxes[..., [0, 2]].clip(0, shape[1])  # x1, x2
        boxes[..., [1, 3]] = boxes[..., [1, 3]].clip(0, shape[0])  # y1, y2
    return boxes

def scale_boxes(img1_shape, boxes, img0_shape, ratio_pad=None, padding=True, xywh=False):
    """
    Rescales bounding boxes (in the format of xyxy by default) from the shape of the image they were originally
    specified in (img1_shape) to the shape of a different image (img0_shape).

    Args:
        img1_shape (tuple): The shape of the image that the bounding boxes are for, in the format of (height, width).
        boxes (torch.Tensor): the bounding boxes of the objects in the image, in the format of (x1, y1, x2, y2)
        img0_shape (tuple): the shape of the target image, in the format of (height, width).
        ratio_pad (tuple): a tuple of (ratio, pad) for scaling the boxes. If not provided, the ratio and pad will be
            calculated based on the size difference between the two images.
        padding (bool): If True, assuming the boxes is based on image augmented by yolo style. If False then do regular
            rescaling.
        xywh (bool): The box format is xywh or not, default=False.

    Returns:
        boxes (torch.Tensor): The scaled bounding boxes, in the format of (x1, y1, x2, y2)
    """
    if ratio_pad is None:  # calculate from img0_shape
        gain = min(img1_shape[0] / img0_shape[0], img1_shape[1] / img0_shape[1])  # gain  = old / new
        pad = (
            round((img1_shape[1] - img0_shape[1] * gain) / 2 - 0.1),
            round((img1_shape[0] - img0_shape[0] * gain) / 2 - 0.1),
        )  # wh padding
    else:
        gain = ratio_pad[0][0]
        pad = ratio_pad[1]

    if padding:
        boxes[..., 0] -= pad[0]  # x padding
        boxes[..., 1] -= pad[1]  # y padding
        if not xywh:
            boxes[..., 2] -= pad[0]  # x padding
            boxes[..., 3] -= pad[1]  # y padding
    boxes[..., :4] /= gain
    return clip_boxes(boxes, img0_shape)

def process_mask(protos, masks_in, bboxes, shape, upsample=False):
    """
    Apply masks to bounding boxes using the output of the mask head.

    Args:
        protos (torch.Tensor): A tensor of shape [mask_dim, mask_h, mask_w].
        masks_in (torch.Tensor): A tensor of shape [n, mask_dim], where n is the number of masks after NMS.
        bboxes (torch.Tensor): A tensor of shape [n, 4], where n is the number of masks after NMS.
        shape (tuple): A tuple of integers representing the size of the input image in the format (h, w).
        upsample (bool): A flag to indicate whether to upsample the mask to the original image size. Default is False.

    Returns:
        (torch.Tensor): A binary mask tensor of shape [n, h, w], where n is the number of masks after NMS, and h and w
            are the height and width of the input image. The mask is applied to the bounding boxes.
    """
    c, mh, mw = protos.shape  # CHW
    ih, iw = shape
    masks = (masks_in @ protos.float().view(c, -1)).view(-1, mh, mw)  # CHW
    width_ratio = mw / iw
    height_ratio = mh / ih

    downsampled_bboxes = bboxes.clone()
    downsampled_bboxes[:, 0] *= width_ratio
    downsampled_bboxes[:, 2] *= width_ratio
    downsampled_bboxes[:, 3] *= height_ratio
    downsampled_bboxes[:, 1] *= height_ratio

    masks = crop_mask(masks, downsampled_bboxes)  # CHW
    if upsample:
        masks = F.interpolate(masks[None], shape, mode="bilinear", align_corners=False)[0]  # CHW
    return masks.gt_(0.0)

def _get_covariance_matrix(boxes):
    """
    Generating covariance matrix from obbs.

    Args:
        boxes (torch.Tensor): A tensor of shape (N, 5) representing rotated bounding boxes, with xywhr format.

    Returns:
        (torch.Tensor): Covariance matrices corresponding to original rotated bounding boxes.
    """
    # Gaussian bounding boxes, ignore the center points (the first two columns) because they are not needed here.
    gbbs = torch.cat((boxes[:, 2:4].pow(2) / 12, boxes[:, 4:]), dim=-1)
    a, b, c = gbbs.split(1, dim=-1)
    cos = c.cos()
    sin = c.sin()
    cos2 = cos.pow(2)
    sin2 = sin.pow(2)
    return a * cos2 + b * sin2, a * sin2 + b * cos2, (a - b) * cos * sin

def batch_probiou(obb1, obb2, eps=1e-7):
    """
    Calculate the prob IoU between oriented bounding boxes, https://arxiv.org/pdf/2106.06072v1.pdf.

    Args:
        obb1 (torch.Tensor | np.ndarray): A tensor of shape (N, 5) representing ground truth obbs, with xywhr format.
        obb2 (torch.Tensor | np.ndarray): A tensor of shape (M, 5) representing predicted obbs, with xywhr format.
        eps (float, optional): A small value to avoid division by zero. Defaults to 1e-7.

    Returns:
        (torch.Tensor): A tensor of shape (N, M) representing obb similarities.
    """
    obb1 = torch.from_numpy(obb1) if isinstance(obb1, np.ndarray) else obb1
    obb2 = torch.from_numpy(obb2) if isinstance(obb2, np.ndarray) else obb2

    x1, y1 = obb1[..., :2].split(1, dim=-1)
    x2, y2 = (x.squeeze(-1)[None] for x in obb2[..., :2].split(1, dim=-1))
    a1, b1, c1 = _get_covariance_matrix(obb1)
    a2, b2, c2 = (x.squeeze(-1)[None] for x in _get_covariance_matrix(obb2))

    t1 = (
        ((a1 + a2) * (y1 - y2).pow(2) + (b1 + b2) * (x1 - x2).pow(2)) / ((a1 + a2) * (b1 + b2) - (c1 + c2).pow(2) + eps)
    ) * 0.25
    t2 = (((c1 + c2) * (x2 - x1) * (y1 - y2)) / ((a1 + a2) * (b1 + b2) - (c1 + c2).pow(2) + eps)) * 0.5
    t3 = (
        ((a1 + a2) * (b1 + b2) - (c1 + c2).pow(2))
        / (4 * ((a1 * b1 - c1.pow(2)).clamp_(0) * (a2 * b2 - c2.pow(2)).clamp_(0)).sqrt() + eps)
        + eps
    ).log() * 0.5
    bd = (t1 + t2 + t3).clamp(eps, 100.0)
    hd = (1.0 - (-bd).exp() + eps).sqrt()
    return 1 - hd

def nms_rotated(boxes, scores, threshold=0.45):
    """
    NMS for oriented bounding boxes using probiou and fast-nms.

    Args:
        boxes (torch.Tensor): Rotated bounding boxes, shape (N, 5), format xywhr.
        scores (torch.Tensor): Confidence scores, shape (N,).
        threshold (float, optional): IoU threshold. Defaults to 0.45.

    Returns:
        (torch.Tensor): Indices of boxes to keep after NMS.
    """
    if len(boxes) == 0:
        return np.empty((0,), dtype=np.int8)
    sorted_idx = torch.argsort(scores, descending=True)
    boxes = boxes[sorted_idx]
    ious = batch_probiou(boxes, boxes).triu_(diagonal=1)
    pick = torch.nonzero(ious.max(dim=0)[0] < threshold).squeeze_(-1)
    return sorted_idx[pick]

def xywh2xyxy(x):
    """
    Convert bounding box coordinates from (x, y, width, height) format to (x1, y1, x2, y2) format where (x1, y1) is the
    top-left corner and (x2, y2) is the bottom-right corner. Note: ops per 2 channels faster than per channel.

    Args:
        x (np.ndarray | torch.Tensor): The input bounding box coordinates in (x, y, width, height) format.

    Returns:
        y (np.ndarray | torch.Tensor): The bounding box coordinates in (x1, y1, x2, y2) format.
    """
    assert x.shape[-1] == 4, f"input shape last dimension expected 4 but input shape is {x.shape}"
    y = torch.empty_like(x) if isinstance(x, torch.Tensor) else np.empty_like(x)  # faster than clone/copy
    xy = x[..., :2]  # centers
    wh = x[..., 2:] / 2  # half width-height
    y[..., :2] = xy - wh  # top left xy
    y[..., 2:] = xy + wh  # bottom right xy
    return y

def non_max_suppression(
    prediction,
    conf_thres=0.25,
    iou_thres=0.45,
    classes=None,
    agnostic=False,
    multi_label=False,
    labels=(),
    max_det=300,
    nc=0,  # number of classes (optional)
    max_time_img=0.05,
    max_nms=30000,
    max_wh=7680,
    in_place=True,
    rotated=False,
):
    """
    Perform non-maximum suppression (NMS) on a set of boxes, with support for masks and multiple labels per box.

    Args:
        prediction (torch.Tensor): A tensor of shape (batch_size, num_classes + 4 + num_masks, num_boxes)
            containing the predicted boxes, classes, and masks. The tensor should be in the format
            output by a model, such as YOLO.
        conf_thres (float): The confidence threshold below which boxes will be filtered out.
            Valid values are between 0.0 and 1.0.
        iou_thres (float): The IoU threshold below which boxes will be filtered out during NMS.
            Valid values are between 0.0 and 1.0.
        classes (List[int]): A list of class indices to consider. If None, all classes will be considered.
        agnostic (bool): If True, the model is agnostic to the number of classes, and all
            classes will be considered as one.
        multi_label (bool): If True, each box may have multiple labels.
        labels (List[List[Union[int, float, torch.Tensor]]]): A list of lists, where each inner
            list contains the apriori labels for a given image. The list should be in the format
            output by a dataloader, with each label being a tuple of (class_index, x1, y1, x2, y2).
        max_det (int): The maximum number of boxes to keep after NMS.
        nc (int, optional): The number of classes output by the model. Any indices after this will be considered masks.
        max_time_img (float): The maximum time (seconds) for processing one image.
        max_nms (int): The maximum number of boxes into torchvision.ops.nms().
        max_wh (int): The maximum box width and height in pixels.
        in_place (bool): If True, the input prediction tensor will be modified in place.
        rotated (bool): If Oriented Bounding Boxes (OBB) are being passed for NMS.

    Returns:
        (List[torch.Tensor]): A list of length batch_size, where each element is a tensor of
            shape (num_boxes, 6 + num_masks) containing the kept boxes, with columns
            (x1, y1, x2, y2, confidence, class, mask1, mask2, ...).
    """
    import torchvision  # scope for faster 'import ultralytics'

    # Checks
    assert 0 <= conf_thres <= 1, f"Invalid Confidence threshold {conf_thres}, valid values are between 0.0 and 1.0"
    assert 0 <= iou_thres <= 1, f"Invalid IoU {iou_thres}, valid values are between 0.0 and 1.0"
    if isinstance(prediction, (list, tuple)):  # YOLOv8 model in validation model, output = (inference_out, loss_out)
        prediction = prediction[0]  # select only inference output
    if classes is not None:
        classes = torch.tensor(classes, device=prediction.device)

    if prediction.shape[-1] == 6:  # end-to-end model (BNC, i.e. 1,300,6)
        output = [pred[pred[:, 4] > conf_thres][:max_det] for pred in prediction]
        if classes is not None:
            output = [pred[(pred[:, 5:6] == classes).any(1)] for pred in output]
        return output

    bs = prediction.shape[0]  # batch size (BCN, i.e. 1,84,6300)
    nc = nc or (prediction.shape[1] - 4)  # number of classes
    nm = prediction.shape[1] - nc - 4  # number of masks
    mi = 4 + nc  # mask start index
    xc = prediction[:, 4:mi].amax(1) > conf_thres  # candidates

    # Settings
    # min_wh = 2  # (pixels) minimum box width and height
    multi_label &= nc > 1  # multiple labels per box (adds 0.5ms/img)

    prediction = prediction.transpose(-1, -2)  # shape(1,84,6300) to shape(1,6300,84)
    if not rotated:
        if in_place:
            prediction[..., :4] = xywh2xyxy(prediction[..., :4])  # xywh to xyxy
        else:
            prediction = torch.cat((xywh2xyxy(prediction[..., :4]), prediction[..., 4:]), dim=-1)  # xywh to xyxy

    output = [torch.zeros((0, 6 + nm), device=prediction.device)] * bs
    for xi, x in enumerate(prediction):  # image index, image inference
        # Apply constraints
        # x[((x[:, 2:4] < min_wh) | (x[:, 2:4] > max_wh)).any(1), 4] = 0  # width-height
        x = x[xc[xi]]  # confidence

        # Cat apriori labels if autolabelling
        if labels and len(labels[xi]) and not rotated:
            lb = labels[xi]
            v = torch.zeros((len(lb), nc + nm + 4), device=x.device)
            v[:, :4] = xywh2xyxy(lb[:, 1:5])  # box
            v[range(len(lb)), lb[:, 0].long() + 4] = 1.0  # cls
            x = torch.cat((x, v), 0)

        # If none remain process next image
        if not x.shape[0]:
            continue

        # Detections matrix nx6 (xyxy, conf, cls)
        box, cls, mask = x.split((4, nc, nm), 1)

        if multi_label:
            i, j = torch.where(cls > conf_thres)
            x = torch.cat((box[i], x[i, 4 + j, None], j[:, None].float(), mask[i]), 1)
        else:  # best class only
            conf, j = cls.max(1, keepdim=True)
            x = torch.cat((box, conf, j.float(), mask), 1)[conf.view(-1) > conf_thres]

        # Filter by class
        if classes is not None:
            x = x[(x[:, 5:6] == classes).any(1)]

        # Check shape
        n = x.shape[0]  # number of boxes
        if not n:  # no boxes
            continue
        if n > max_nms:  # excess boxes
            x = x[x[:, 4].argsort(descending=True)[:max_nms]]  # sort by confidence and remove excess boxes

        # Batched NMS
        c = x[:, 5:6] * (0 if agnostic else max_wh)  # classes
        scores = x[:, 4]  # scores
        if rotated:
            boxes = torch.cat((x[:, :2] + c, x[:, 2:4], x[:, -1:]), dim=-1)  # xywhr
            i = nms_rotated(boxes, scores, iou_thres)
        else:
            boxes = x[:, :4] + c  # boxes (offset by class)
            i = torchvision.ops.nms(boxes, scores, iou_thres)  # NMS
        i = i[:max_det]  # limit detections

        # # Experimental
        # merge = False  # use merge-NMS
        # if merge and (1 < n < 3E3):  # Merge NMS (boxes merged using weighted mean)
        #     # Update boxes as boxes(i,4) = weights(i,n) * boxes(n,4)
        #     from .metrics import box_iou
        #     iou = box_iou(boxes[i], boxes) > iou_thres  # IoU matrix
        #     weights = iou * scores[None]  # box weights
        #     x[i, :4] = torch.mm(weights, x[:, :4]).float() / weights.sum(1, keepdim=True)  # merged boxes
        #     redundant = True  # require redundant detections
        #     if redundant:
        #         i = i[iou.sum(1) > 1]  # require redundancy

        output[xi] = x[i]

    return output

def image2base64(img: np.ndarray, ratio: float = 1.0, add_mark: bool= True):
        image = Image.fromarray(img)
        w,h = image.size
        new_W = int(ratio * w)
        new_H = int(ratio * h)
        image = image.resize(size=(new_W, new_H))
        buffered = io.BytesIO()
        image.save(buffered,format='PNG')
        pre = ''
        if add_mark:
            pre = 'data:image/png;base64,'
        img_b64 = pre + base64.b64encode(buffered.getvalue()).decode('utf-8')
        return img_b64

class Crop():
    def __call__(self, img, xyxy):
        x1,y1,x2,y2 = xyxy
        return img[:,y1:y2,x1:x2]

class Object():
    def __init__(self, 
                 coord:list[int], 
                 conf: float = None, 
                 mask: np.ndarray = None ,
                 track_id: int = None, 
                 index: int = None,
                 id_global:str = None,
                 mode :Literal['xyxy', 'xywh'] = 'xywh'):
        self.coord = coord
        self.track_id = track_id
        self.index = index
        self.mask = mask
        self.conf = conf
        self.mode = mode
        self.id = id_global

    def crop(self, image: torch.Tensor):
        if self.mode == 'xyxy':
            x1,y1,x2,y2 = self.coord
            img = image[:,y1:y2,x1:x2]
        else:
            x,y,w,h = self.coord
            img = image[:,y:y + h, x:x + w]
        return img
    
    def json(self):
        return {"index":self.index, "bbox":self.coord, "track_id":self.track_id}

def _tlwh(tlbr: np.ndarray):
    if isinstance(tlbr,list):
        tlbr = np.array(tlbr)
    return np.concatenate([tlbr[:2],tlbr[2:] - tlbr[:2]])

def _clip(bbox, image_shape):
    bbox[[0,2]] = torch.clip(bbox[[0,2]],0,image_shape[2])
    bbox[[1,3]] = torch.clip(bbox[[1,3]],0,image_shape[1])
    return bbox

def calculate_iou(boxes1: torch.Tensor, boxes2: torch.Tensor):
    """
    Calculate IoU between two sets of bounding boxes
    
    Args:
        boxes1 (torch.Tensor): Bounding boxes with shape (N, 4)
                              Format: [x1, y1, x2, y2] where (x1, y1) is top-left and (x2, y2) is bottom-right
        boxes2 (torch.Tensor): Bounding boxes with shape (M, 4)
                              Format: [x1, y1, x2, y2]
    
    Returns:
        torch.Tensor: IoU values with shape (N, M)
    """
    # Get the coordinates of bounding boxes
    x1_1, y1_1, x2_1, y2_1 = boxes1[:, 0], boxes1[:, 1], boxes1[:, 2], boxes1[:, 3]
    x1_2, y1_2, x2_2, y2_2 = boxes2[:, 0], boxes2[:, 1], boxes2[:, 2], boxes2[:, 3]
    
    # Calculate areas
    areas1 = (x2_1 - x1_1) * (y2_1 - y1_1)
    areas2 = (x2_2 - x1_2) * (y2_2 - y1_2)
    
    # Calculate intersection coordinates
    x1_i = torch.max(x1_1.unsqueeze(1), x1_2.unsqueeze(0))
    y1_i = torch.max(y1_1.unsqueeze(1), y1_2.unsqueeze(0))
    x2_i = torch.min(x2_1.unsqueeze(1), x2_2.unsqueeze(0))
    y2_i = torch.min(y2_1.unsqueeze(1), y2_2.unsqueeze(0))
    
    # Calculate intersection area
    width = torch.clamp(x2_i - x1_i, min=0)
    height = torch.clamp(y2_i - y1_i, min=0)
    intersection = width * height
    
    # Calculate union area
    union = areas1.unsqueeze(1) + areas2.unsqueeze(0) - intersection
    
    # Calculate IoU
    iou = intersection / union
    
    return iou



def bbox_polygon_iou_pytorch(bbox_tensor, polygon_coords):
    """
    Calculate the IoU (Intersection over Union) between a bounding box and a polygon using PyTorch.
    
    Parameters:
    bbox_tensor (torch.Tensor): Bounding box coordinates in format (minx, miny, maxx, maxy)
    polygon_coords (list): List of (x, y) tuples representing polygon vertices
    
    Returns:
    torch.Tensor: IoU value between 0 and 1
    """
    # Convert bbox tensor to tuple for Shapely
    bbox = tuple(bbox_tensor.cpu().numpy()) if bbox_tensor.is_cuda else tuple(bbox_tensor.numpy())
    
    # Create Shapely geometries
    bbox_geom = box(*bbox)
    polygon_geom = Polygon(polygon_coords)
    
    # Calculate intersection area
    intersection = bbox_geom.intersection(polygon_geom).area
    
    # Calculate union area
    union = bbox_geom.union(polygon_geom).area
    
    # Calculate IoU
    if union == 0:
        return torch.tensor(0.0)
    iou = intersection / union
    
    # Convert back to pytorch tensor
    return torch.tensor(iou, dtype=torch.float32, device=bbox_tensor.device)

def rasterize_polygon_pytorch(polygon_coords, img_size=(28, 28), device='cpu'):
    """
    Rasterize a polygon into a binary mask using PyTorch.
    
    Parameters:
    polygon_coords (list): List of (x, y) tuples representing polygon vertices
    img_size (tuple): Size of the output mask (height, width)
    device (str): PyTorch device to use
    
    Returns:
    torch.Tensor: Binary mask with 1s inside the polygon
    """
    # Convert polygon coordinates to tensor
    polygon_tensor = torch.tensor(polygon_coords, dtype=torch.float32, device=device)
    
    # Create coordinate grid
    y_coords = torch.linspace(0, 1, img_size[0], device=device)
    x_coords = torch.linspace(0, 1, img_size[1], device=device)
    grid_y, grid_x = torch.meshgrid(y_coords, x_coords, indexing='ij')
    grid_points = torch.stack([grid_x.flatten(), grid_y.flatten()], dim=1)
    
    # Scale polygon coordinates to [0, 1] for the rasterization
    min_coords = torch.min(polygon_tensor, dim=0)[0]
    max_coords = torch.max(polygon_tensor, dim=0)[0]
    polygon_scaled = (polygon_tensor - min_coords) / (max_coords - min_coords)
    
    # Create mask using point-in-polygon test
    mask = point_in_polygon_pytorch(grid_points, polygon_scaled, device)
    mask = mask.reshape(img_size)
    
    return mask

def point_in_polygon_pytorch(points, polygon, device='cpu'):
    """
    Determine if points are inside a polygon using PyTorch.
    Uses the ray casting algorithm.
    
    Parameters:
    points (torch.Tensor): Points to test, shape (N, 2)
    polygon (torch.Tensor): Polygon vertices, shape (M, 2)
    device (str): PyTorch device to use
    
    Returns:
    torch.Tensor: Boolean tensor of shape (N,) with True for points inside
    """
    # Number of points and polygon vertices
    n_points = points.shape[0]
    n_vertices = polygon.shape[0]
    
    # Initialize result tensor
    inside = torch.zeros(n_points, dtype=torch.bool, device=device)
    
    # Ray casting algorithm
    for i in range(n_vertices):
        j = (i + 1) % n_vertices
        
        # Edge vertices
        vertex_i = polygon[i]
        vertex_j = polygon[j]
        
        # Conditions for ray intersection
        cond1 = (vertex_i[1] > points[:, 1]) != (vertex_j[1] > points[:, 1])
        cond2 = points[:, 0] < (vertex_j[0] - vertex_i[0]) * (points[:, 1] - vertex_i[1]) / (vertex_j[1] - vertex_i[1]) + vertex_i[0]
        
        # Toggle inside status for points that pass both conditions
        mask = cond1 & cond2
        inside[mask] = ~inside[mask]
    
    return inside

def pytorch_pure_iou(bbox_tensor, polygon_coords, raster_size=(100, 100), device='cpu'):
    """
    Calculate IoU using pure PyTorch by rasterizing both geometries.
    
    Parameters:
    bbox_tensor (torch.Tensor): Bounding box coordinates in format (minx, miny, maxx, maxy)
    polygon_coords (list): List of (x, y) tuples representing polygon vertices
    raster_size (tuple): Size of the rasterization grid
    device (str): PyTorch device to use
    
    Returns:
    torch.Tensor: IoU value between 0 and 1
    """
    # Convert bbox to polygon
    minx, miny, maxx, maxy = bbox_tensor
    bbox_polygon = [(minx.item(), miny.item()), (maxx.item(), miny.item()), 
                   (maxx.item(), maxy.item()), (minx.item(), maxy.item())]
    
    # Rasterize both geometries
    bbox_mask = rasterize_polygon_pytorch(bbox_polygon, raster_size, device)
    polygon_mask = rasterize_polygon_pytorch(polygon_coords, raster_size, device)
    
    # Calculate intersection and union
    intersection = torch.logical_and(bbox_mask, polygon_mask).sum().float()
    union = torch.logical_or(bbox_mask, polygon_mask).sum().float()
    
    # Calculate IoU
    if union == 0:
        return torch.tensor(0.0, device=device)
    iou = intersection / union
    
    return iou


def batch_bbox_polygon_iou(batch_bboxes, polygon_coords):
    """
    Calculate IoU between a batch of bounding boxes and a polygon.
    
    Parameters:
    batch_bboxes (torch.Tensor): Tensor of shape (N, 4) where each row contains (minx, miny, maxx, maxy)
    polygon_coords (list): List of (x, y) tuples representing polygon vertices
    
    Returns:
    torch.Tensor: Tensor of shape (N,) containing IoU values for each bbox
    """
    # Get device information
    device = batch_bboxes.device
    batch_size = batch_bboxes.shape[0]
    
    # Create polygon geometry once
    polygon_geom = Polygon(polygon_coords)
    
    # Initialize output IoU tensor
    iou_values = torch.zeros(batch_size, dtype=torch.float32, device=device)
    
    # Process each bbox
    for i in range(batch_size):
        # Extract bbox coordinates
        bbox = batch_bboxes[i].cpu().numpy() if batch_bboxes.is_cuda else batch_bboxes[i].numpy()
        
        # Create bbox geometry
        bbox_geom = box(*bbox)
        
        # Skip calculation if there's no overlap
        if not bbox_geom.intersects(polygon_geom):
            iou_values[i] = 0.0
            continue
            
        # Calculate intersection area
        intersection = bbox_geom.intersection(polygon_geom).area
        
        # Calculate union area
        union = bbox_geom.union(polygon_geom).area
        
        # Calculate IoU
        if union > 0:
            iou_values[i] = intersection / union
    
    return iou_values

def batch_bbox_polygon_iou_pure_pytorch(batch_bboxes, polygon_coords, raster_size=(100, 100)):
    """
    Calculate IoU between a batch of bounding boxes and a polygon using pure PyTorch.
    This is approximate but differentiable.
    
    Parameters:
    batch_bboxes (torch.Tensor): Tensor of shape (N, 4) where each row contains (minx, miny, maxx, maxy)
    polygon_coords (list): List of (x, y) tuples representing polygon vertices
    raster_size (tuple): Size of the rasterization grid
    
    Returns:
    torch.Tensor: Tensor of shape (N,) containing IoU values for each bbox
    """
    device = batch_bboxes.device
    batch_size = batch_bboxes.shape[0]
    
    # Convert polygon to tensor
    polygon_tensor = torch.tensor(polygon_coords, dtype=torch.float32, device=device)
    
    # Rasterize the polygon once
    polygon_mask = rasterize_polygon_pytorch(polygon_coords, raster_size, device)
    
    # Initialize output IoU tensor
    iou_values = torch.zeros(batch_size, dtype=torch.float32, device=device)
    
    # Process each bbox
    for i in range(batch_size):
        # Convert bbox to polygon vertices
        bbox = batch_bboxes[i]
        minx, miny, maxx, maxy = bbox
        bbox_polygon = [(minx.item(), miny.item()), (maxx.item(), miny.item()),
                        (maxx.item(), maxy.item()), (minx.item(), maxy.item())]
        
        # Rasterize the bbox
        bbox_mask = rasterize_polygon_pytorch(bbox_polygon, raster_size, device)
        
        # Calculate intersection and union
        intersection = torch.logical_and(bbox_mask, polygon_mask).sum().float()
        # union = torch.logical_or(bbox_mask, polygon_mask).sum().float()
        union = bbox_mask.sum().float()
        
        # Calculate IoU
        if union > 0:
            iou_values[i] = intersection / union
    
    return iou_values

def rasterize_polygon_pytorch(polygon_coords, img_size=(100, 100), device='cpu'):
    """
    Rasterize a polygon into a binary mask using PyTorch.
    
    Parameters:
    polygon_coords (list): List of (x, y) tuples representing polygon vertices
    img_size (tuple): Size of the output mask (height, width)
    device (str): PyTorch device to use
    
    Returns:
    torch.Tensor: Binary mask with 1s inside the polygon
    """
    # Convert polygon coordinates to tensor
    polygon_tensor = torch.tensor(polygon_coords, dtype=torch.float32, device=device)
    
    # # Get bounding box of polygon for scaling
    # min_x = min(p[0] for p in polygon_coords)
    # max_x = max(p[0] for p in polygon_coords)
    # min_y = min(p[1] for p in polygon_coords)
    # max_y = max(p[1] for p in polygon_coords)
    
    # Create coordinate grid
    h, w = img_size
    y_range = torch.arange(0, h, dtype=torch.float32, device=device)
    x_range = torch.arange(0, w, dtype=torch.float32, device=device)
    grid_y, grid_x = torch.meshgrid(y_range, x_range, indexing='ij')
    points = torch.stack((grid_x.flatten(), grid_y.flatten()), dim=1)
    
    # Test points inside polygon
    mask = points_in_polygon_batch(points, polygon_tensor, device)
    
    return mask.reshape(h, w)

def points_in_polygon_batch(points, polygon, device='cpu'):
    """
    Vectorized point-in-polygon test using PyTorch.
    
    Parameters:
    points (torch.Tensor): Points to test, shape (N, 2)
    polygon (torch.Tensor): Polygon vertices, shape (M, 2)
    device (str): PyTorch device
    
    Returns:
    torch.Tensor: Boolean tensor of shape (N,)
    """
    n_points = points.shape[0]
    n_vertices = polygon.shape[0]
    
    # Result array
    inside = torch.zeros(n_points, dtype=torch.bool, device=device)
    
    # For each edge
    for i in range(n_vertices):
        j = (i + 1) % n_vertices
        
        # Edge vertices
        v_i = polygon[i]
        v_j = polygon[j]
        
        # Edge crossing conditions
        # (y_i > point_y) != (y_j > point_y) AND point_x < edge_slope * (point_y - y_i) + x_i
        cond1 = (v_i[1] > points[:, 1]) != (v_j[1] > points[:, 1])
        
        # Handle vertical edges
        if torch.abs(v_j[1] - v_i[1]) < 1e-10:
            continue
            
        # Calculate x-coordinate of intersection
        x_intersect = (v_j[0] - v_i[0]) * (points[:, 1] - v_i[1]) / (v_j[1] - v_i[1]) + v_i[0]
        cond2 = points[:, 0] < x_intersect
        
        # Toggle inside status
        inside = inside ^ (cond1 & cond2)
    
    return inside


def main():
    # Set device
    device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
    
    # Example usage
    # Define a polygon
    polygon = [(2, 2), (8, 2), (8, 8), (2, 8)]
    
    # Define some test bounding boxes (minx, miny, maxx, maxy)
    bbox1 = torch.tensor([3, 3, 7, 7], dtype=torch.float32, device=device)  # Completely inside
    bbox2 = torch.tensor([0, 0, 5, 5], dtype=torch.float32, device=device)  # Partial overlap
    bbox3 = torch.tensor([9, 9, 15, 15], dtype=torch.float32, device=device)  # No overlap
    
    # Calculate and print IoUs
    print(f"IoU for bbox1 (Shapely): {bbox_polygon_iou_pytorch(bbox1, polygon):.4f}")
    print(f"IoU for bbox2 (Shapely): {bbox_polygon_iou_pytorch(bbox2, polygon):.4f}")
    print(f"IoU for bbox3 (Shapely): {bbox_polygon_iou_pytorch(bbox3, polygon):.4f}")
    
    # Calculate pure PyTorch IoUs (more approximate but fully differentiable)
    print(f"IoU for bbox1 (Pure PyTorch): {pytorch_pure_iou(bbox1, polygon, device=device):.4f}")
    print(f"IoU for bbox2 (Pure PyTorch): {pytorch_pure_iou(bbox2, polygon, device=device):.4f}")
    print(f"IoU for bbox3 (Pure PyTorch): {pytorch_pure_iou(bbox3, polygon, device=device):.4f}")

if __name__ == "__main__":
    main()