import cv2
import multiprocessing as mp
import argparse
import os
import signal
import sys
import time
from detect import ObjectProcess
from settings import env, LOGGER

# Global variables for signal handling
stop_event = None
detect_process = None
image_queue = None
video_capture = None

def signal_handler(sig, frame):
    """Handle interrupt signals gracefully"""
    global stop_event, detect_process, image_queue, video_capture
    
    LOGGER.info(f"Received signal {sig}, initiating graceful shutdown")
    
    if stop_event:
        stop_event.set()
    
    # Give the process a moment to start shutting down
    time.sleep(1)
    
    # Clean up resources
    if video_capture and video_capture.isOpened():
        video_capture.release()
        LOGGER.info("Video capture released")
    
    # Don't exit immediately - let the main thread handle the cleanup
    # This prevents the KeyboardInterrupt from propagating to child processes

def parse_args():
    parser = argparse.ArgumentParser(description='Person Detection and Re-identification System')
    parser.add_argument('--video', type=str, default=env.video_url,
                        help='Path to video file or camera URL')
    parser.add_argument('--skip_frames', type=int, default=env.skip_frame,
                        help='Number of frames to skip between processing')
    parser.add_argument('--output_dir', type=str, default='output',
                        help='Directory to save results')
    parser.add_argument('--debug', action='store_true',
                        help='Enable debug mode with visualization')
    return parser.parse_args()

if __name__ == '__main__':
    # Parse command line arguments
    args = parse_args()
    
    # Update environment settings
    env.video_url = args.video
    env.skip_frame = args.skip_frames
    env.debug = args.debug
    
    # Create output directory if it doesn't exist
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Initialize multiprocessing
    mp.set_start_method('spawn')
    imageQ = mp.Queue(100)
    stop_event = mp.Event()
    
    # Set up global variables for signal handler
    image_queue = imageQ
    
    # Set up signal handlers
    # signal.signal(signal.SIGINT, signal_handler)  # Ctrl+C
    # signal.signal(signal.SIGTERM, signal_handler)  # Termination signal
    
    # Initialize person detection and tracking process
    LOGGER.info(f"Initializing person detection with YOLO and tracking with DeepSORT")
    detect = ObjectProcess(
        content_queue=imageQ,
        event=stop_event,
        output_dir=args.output_dir
    )
    detect.start()
    
    # Set global variable for signal handler
    detect_process = detect
    
    # Open video capture
    LOGGER.info(f"Opening video source: {env.video_url}")
    cap = cv2.VideoCapture(env.video_url)
    
    # Set global variable for signal handler
    video_capture = cap
    
    if not cap.isOpened():
        LOGGER.error(f"Failed to open video source: {env.video_url}")
        stop_event.set()
        detect.join()
        detect.terminate()
        exit(1)
    
    # Process video frames
    counter = 0
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT)) if env.video_url.startswith(('/', '.')) else -1
    fps = cap.get(cv2.CAP_PROP_FPS)
    
    LOGGER.info(f"Video FPS: {fps}, Total frames: {total_frames if total_frames > 0 else 'unknown (live stream)'}")
    LOGGER.info(f"Processing every {env.skip_frame} frames")
    
    try:
        while True:
            if stop_event.is_set():
                LOGGER.info('-----------STOP------------')
                break
                
            ret, frame = cap.read()
            counter += 1
            
            if not ret:
                LOGGER.info("End of video stream reached")
                break
                
            if counter % env.skip_frame != 0:
                continue
                
            if total_frames > 0:
                progress = (counter / total_frames) * 100
                if counter % (env.skip_frame * 10) == 0:
                    LOGGER.info(f"Processing progress: {progress:.1f}% (frame {counter}/{total_frames})")
            
            imageQ.put((counter, frame))
            
    except KeyboardInterrupt:
        LOGGER.info("Processing interrupted by user, initiating graceful shutdown")
        # Signal handler should have already set the stop_event
        exit(0)
    except Exception as e:
        LOGGER.exception(f"Error during video processing: {e}")
    finally:
        LOGGER.info("Cleaning up resources")
        try:
            # Signal the detection process to stop
            stop_event.set()
            
            # Add a sentinel value to the queue to ensure the process exits
            imageQ.put((counter + 1, None))
            
            # Release video capture
            if cap.isOpened():
                cap.release()
                LOGGER.info("Video capture released")
            
            # Wait for the detection process to finish with a timeout
            LOGGER.info("Waiting for detection process to finish (timeout: 10 seconds)")
            detect.join(timeout=10)
            
            # If the process is still alive after timeout, terminate it
            if detect.is_alive():
                LOGGER.warning("Detection process did not exit gracefully, forcing termination")
                detect.terminate()
                detect.join(timeout=5)
                
                # If still alive, kill it
                if detect.is_alive():
                    LOGGER.error("Failed to terminate detection process, killing it")
                    detect.kill()
            else:
                LOGGER.info("Detection process exited gracefully")
                
            LOGGER.info("Processing completed")
        except Exception as cleanup_error:
            LOGGER.error(f"Error during cleanup: {cleanup_error}")
        finally:
            # Always exit cleanly
            LOGGER.info("Exiting application")
            exit(0)