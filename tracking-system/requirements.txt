# Core dependencies
opencv-python>=4.5.0
numpy>=1.20.0
torch>=1.10.0
torchvision>=0.11.0
onnxruntime-gpu>=1.10.0  # Use onnxruntime if GPU not available
pydantic>=2.0.0
pydantic-settings>=2.0.0
shapely>=1.8.0

# Redis for vector similarity search
redis>=4.5.0
redis-om>=0.1.0

# Utilities
scipy>=1.8.0
av>=10.0.0  # For video processing
tqdm>=4.64.0  # For progress bars
pillow>=9.0.0  # For image processing

# Optional dependencies
# boto3>=1.24.0  # For S3 storage
# pymilvus>=2.2.0  # For Milvus vector database
# confluent-kafka>=2.0.0  # For Kafka messaging