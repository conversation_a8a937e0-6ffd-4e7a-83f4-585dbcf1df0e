import json

def main():
    # Load the total30p.json file
    with open('total-04042025.json', 'r') as f:
        total_data = json.load(f)
    
    # Load the info30.json file
    with open('info-04042025-filtered.json', 'r') as f:
        info_data = json.load(f)
    
    # Concatenate all merged groups from all zones
    all_merged_groups = []
    group_name_ids = []
    reid_to_group_mapping = {}
    
    # Process each zone
    for zone_name, zone_data in total_data.items():
        # Replace spaces with underscores in zone name
        zone_name_clean = zone_name.replace(" ", "_")
        
        # Process each merged group in the zone
        for i, group in enumerate(zone_data['mergedGroups']):
            # Add this group to the concatenated list
            all_merged_groups.append(group['ids'])
            
            # Create the new group ID (index_zone_name)
            group_name_ids.append(f"{len(all_merged_groups)-1}_{zone_name_clean}")
            

            
    data = []
    # Now update the trackids in info30.json
    for frame in info_data:
        timecode = round(frame['index']/25,4)
        ids = []
        for obj in frame['objects']:
            tid = str(obj['trackid'])
            for i,group_ids in enumerate(all_merged_groups):
                if tid in group_ids:
                    break
            ids.append(group_name_ids[i])
        data.append({
            'timecode': timecode,
            'ids': list(dict.fromkeys(ids))
        })
                # Replace trackid with the corresponding merged group index + zone name
    
    # Save the updated info30.json
    with open('updated_info-04042025.json', 'w') as f:
        json.dump(data, f)
    
    # Save the concatenated merged groups (optional)
    concat_data = {'mergedGroups': all_merged_groups}
    with open('concatenated_merged_groups.json', 'w') as f:
        json.dump(concat_data, f)
    
    print(f"Successfully processed {len(all_merged_groups)} merged groups from {len(total_data)} zones")
    print(f"Updated trackids in {len(info_data)} frames")
    print("Results saved to 'updated_info.json' and 'concatenated_merged_groups.json'")

if __name__ == "__main__":
    main()