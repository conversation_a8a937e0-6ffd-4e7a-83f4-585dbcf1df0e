import cv2
import numpy as np
import logging
import os
import time
from detector import PersonDetector

# Configure logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('TestImage')

def create_sample_image():
    """Create a sample image with a person-like shape"""
    # Create a blank image
    img = np.zeros((480, 640, 3), dtype=np.uint8)
    
    # Draw a person-like shape (rectangle for body)
    cv2.rectangle(img, (300, 100), (340, 300), (255, 255, 255), -1)
    
    # Draw a circle for head
    cv2.circle(img, (320, 70), 30, (255, 255, 255), -1)
    
    # Save the image
    cv2.imwrite('sample_person.jpg', img)
    logger.info("Created sample image: sample_person.jpg")
    
    return img

def test_detector():
    """Test the detector with a sample image"""
    # Create or load sample image
    if os.path.exists('sample_person.jpg'):
        logger.info("Loading existing sample image")
        img = cv2.imread('sample_person.jpg')
    else:
        logger.info("Creating new sample image")
        img = create_sample_image()
    
    # Initialize detector
    logger.info("Initializing detector...")
    detector = PersonDetector(conf_threshold=0.3)  # Lower threshold for test image
    
    # Run detection
    logger.info("Running detection")
    detections = detector.detect(img)
    
    # Log results
    logger.info(f"Detected {len(detections)} objects")
    for i, detection in enumerate(detections):
        x1, y1, x2, y2, conf = detection
        logger.info(f"Detection {i+1}: bbox={[x1, y1, x2, y2]}, confidence={conf:.2f}")
    
    # Draw detections
    for x1, y1, x2, y2, conf in detections:
        # Draw rectangle
        cv2.rectangle(img, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 2)
        
        # Add label
        label = f"Person: {conf:.2f}"
        cv2.putText(img, label, (int(x1), int(y1) - 10), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
    
    # Save the output image instead of displaying it
    output_path = 'detection_result.jpg'
    cv2.imwrite(output_path, img)
    logger.info(f"Saved detection result to {output_path}")
    
    return len(detections) > 0

if __name__ == "__main__":
    test_detector()