import cv2
import numpy as np
import logging
import time
from collections import defaultdict

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('PersonTracker')

class SimpleTracker:
    """
    A simple tracker implementation as fallback when OpenCV trackers are not available
    Uses IoU-based tracking
    """
    def __init__(self):
        self.bbox = None
        
    def init(self, frame, bbox):
        """Initialize the tracker with a bounding box"""
        self.bbox = bbox
        return True
        
    def update(self, frame):
        """Return the current bounding box"""
        return True, self.bbox

class OpenCVTracker:
    """
    Multi-object tracker using OpenCV's built-in trackers
    Based on the PyImageSearch article approach
    """
    def __init__(self, tracker_type="KCF", max_disappeared=30):
        """
        Initialize the tracker
        
        Args:
            tracker_type (str): Type of OpenCV tracker to use
                Options: KCF, CSRT, MOSSE, MIL, etc.
            max_disappeared (int): Maximum number of frames a track can be lost before being removed
        """
        self.tracker_type = tracker_type
        self.max_disappeared = max_disappeared
        
        # Initialize trackers and track IDs
        self.trackers = []
        self.track_ids = {}
        self.next_id = 1
        self.tracks_age = {}
        self.tracked_boxes = []
        
        logger.info(f"Initialized OpenCV Tracker with type: {tracker_type}")
        
    def _create_tracker(self):
        """Create a new tracker instance based on tracker_type"""
        # In newer versions of OpenCV (4.5.1+), the tracking API has changed
        try:
            # Try the newer API first
            tracker = cv2.TrackerCreator_create(self.tracker_type)
            logger.debug(f"Created tracker using new API: {self.tracker_type}")
            return tracker
        except (AttributeError, cv2.error):
            try:
                # Try the legacy API
                if self.tracker_type == "KCF":
                    return cv2.legacy.TrackerKCF_create()
                elif self.tracker_type == "CSRT":
                    return cv2.legacy.TrackerCSRT_create()
                elif self.tracker_type == "MOSSE":
                    return cv2.legacy.TrackerMOSSE_create()
                elif self.tracker_type == "MIL":
                    return cv2.legacy.TrackerMIL_create()
                elif self.tracker_type == "BOOSTING":
                    return cv2.legacy.TrackerBoosting_create()
                elif self.tracker_type == "TLD":
                    return cv2.legacy.TrackerTLD_create()
                elif self.tracker_type == "MEDIANFLOW":
                    return cv2.legacy.TrackerMedianFlow_create()
                else:
                    logger.warning(f"Unknown tracker type: {self.tracker_type}, using CSRT")
                    return cv2.legacy.TrackerCSRT_create()
            except (AttributeError, cv2.error):
                # If both fail, use a simple tracker implementation
                logger.warning(f"OpenCV tracking API not available, using simple tracker")
                return SimpleTracker()
            
    def init(self, frame, detections):
        """
        Initialize trackers with detections
        
        Args:
            frame (numpy.ndarray): Current frame
            detections (list): List of detections [x1, y1, x2, y2, conf]
        """
        # Reset trackers
        self.trackers = []
        self.track_ids = {}
        self.tracks_age = {}
        self.tracked_boxes = []
        
        # Initialize a tracker for each detection
        for i, detection in enumerate(detections):
            x1, y1, x2, y2 = detection[:4]
            
            # Convert to (x, y, width, height) format for OpenCV trackers
            bbox = (int(x1), int(y1), int(x2-x1), int(y2-y1))
            
            # Ensure the box is valid (non-zero width and height)
            if bbox[2] <= 0 or bbox[3] <= 0:
                logger.warning(f"Skipping invalid box: {bbox}")
                continue
                
            # Create and initialize tracker
            tracker = self._create_tracker()
            success = tracker.init(frame, bbox)
            
            if success:
                # Add to trackers list
                tracker_idx = len(self.trackers)
                self.trackers.append(tracker)
                
                # Assign ID to this track
                self.track_ids[tracker_idx] = self.next_id
                self.tracks_age[self.next_id] = 0
                self.next_id += 1
                
                # Add to tracked boxes
                self.tracked_boxes.append(bbox)
                
                logger.debug(f"Initialized tracker {tracker_idx} with ID {self.next_id-1}")
                
        logger.info(f"Initialized {len(self.trackers)} trackers")
        
    def update(self, frame):
        """
        Update trackers with new frame
        
        Args:
            frame (numpy.ndarray): Current frame
            
        Returns:
            list: List of tracking results [x1, y1, x2, y2, track_id]
        """
        if not self.trackers:
            return []
            
        # Update existing trackers
        updated_boxes = []
        trackers_to_remove = []
        
        for i, (tracker, track_id) in enumerate(zip(self.trackers, self.track_ids.values())):
            try:
                success, box = tracker.update(frame)
            except cv2.error as e:
                logger.error(f"Error updating tracker: {e}")
                success = False
                box = None
                
            if success:
                updated_boxes.append(box)
                self.tracks_age[track_id] = 0  # Reset age for successful tracks
            else:
                self.tracks_age[track_id] += 1
                if self.tracks_age[track_id] > self.max_disappeared:
                    trackers_to_remove.append(i)
                updated_boxes.append(None)  # Placeholder for failed track
                
        # Remove trackers that haven't been updated for max_disappeared frames
        for idx in sorted(trackers_to_remove, reverse=True):
            try:
                # Get the track_id before removing the tracker
                track_id = None
                for k, v in self.track_ids.items():
                    if k == idx:
                        track_id = v
                        break
                
                if track_id is not None:
                    # Remove the tracker and its age
                    del self.trackers[idx]
                    del self.tracks_age[track_id]
                    
                    # Create a new track_ids dictionary with updated indices
                    new_track_ids = {}
                    for k, v in self.track_ids.items():
                        if k < idx:
                            new_track_ids[k] = v
                        elif k > idx:
                            new_track_ids[k-1] = v
                    
                    self.track_ids = new_track_ids
                    
                    logger.debug(f"Removed tracker at index {idx} with ID {track_id}")
            except Exception as e:
                logger.error(f"Error removing tracker: {e}")
                             
        # Store current tracked boxes
        self.tracked_boxes = [box for box in updated_boxes if box is not None]
        
        # Convert results to [x1, y1, x2, y2, track_id] format
        results = []
        for i, box in enumerate(self.tracked_boxes):
            # Make sure we have a valid index in track_ids
            track_id = None
            for idx, tid in self.track_ids.items():
                if idx == i:
                    track_id = tid
                    break
            
            if track_id is not None:
                # Convert from (x, y, width, height) to [x1, y1, x2, y2]
                x, y, w, h = box
                bbox = [x, y, x+w, y+h]
                results.append(bbox + [track_id])
        
        # Debug logging
        logger.debug(f"Tracking results: {len(results)} tracks")
        return results
        
    def add_detections(self, frame, detections, iou_threshold=0.5):
        """
        Add new detections to existing trackers
        
        Args:
            frame (numpy.ndarray): Current frame
            detections (list): List of detections [x1, y1, x2, y2, conf]
            iou_threshold (float): IOU threshold for matching detections with existing tracks
            
        Returns:
            list: List of tracking results [x1, y1, x2, y2, track_id]
        """
        # Update existing trackers
        tracking_results = self.update(frame)
        
        # Add new detections that don't match existing tracks
        for detection in detections:
            x1, y1, x2, y2 = detection[:4]
            
            # Check if this detection overlaps with any existing track
            is_new = True
            for result in tracking_results:
                tx1, ty1, tx2, ty2 = result[:4]
                
                # Calculate IoU
                intersection_x1 = max(x1, tx1)
                intersection_y1 = max(y1, ty1)
                intersection_x2 = min(x2, tx2)
                intersection_y2 = min(y2, ty2)
                
                if intersection_x2 > intersection_x1 and intersection_y2 > intersection_y1:
                    intersection_area = (intersection_x2 - intersection_x1) * (intersection_y2 - intersection_y1)
                    detection_area = (x2 - x1) * (y2 - y1)
                    track_area = (tx2 - tx1) * (ty2 - ty1)
                    union_area = detection_area + track_area - intersection_area
                    iou = intersection_area / union_area
                    
                    if iou > iou_threshold:
                        is_new = False
                        break
                        
            if is_new:
                # Convert to (x, y, width, height) format for OpenCV trackers
                bbox = (int(x1), int(y1), int(x2-x1), int(y2-y1))
                
                # Create and initialize tracker
                tracker = self._create_tracker()
                success = tracker.init(frame, bbox)
                
                if success:
                    # Add to trackers list
                    tracker_idx = len(self.trackers)
                    self.trackers.append(tracker)
                    
                    # Assign ID to this track
                    track_id = self.next_id
                    self.track_ids[tracker_idx] = track_id
                    self.tracks_age[track_id] = 0
                    self.next_id += 1
                    
                    # Add to tracked boxes
                    self.tracked_boxes.append(bbox)
                    
                    # Add to results
                    tracking_results.append([x1, y1, x2, y2, track_id])
                    
                    logger.debug(f"Added new tracker {tracker_idx} with ID {track_id}")
                    
        return tracking_results


class SimpleDeepSORT:
    """
    Simplified implementation of DeepSORT tracking
    Uses IoU matching for simplicity (without Kalman filter or appearance features)
    """
    def __init__(self, max_age=100, min_iou=0.4):
        """
        Initialize the tracker
        
        Args:
            max_age (int): Maximum number of frames a track can be lost before being removed
            min_iou (float): Minimum IoU for matching detections with tracks
        """
        self.max_age = max_age
        self.min_iou = min_iou
        self.tracks = {}  # Dictionary of track_id -> {bbox, age}
        self.next_id = 1
        
        logger.info(f"Initialized Simple DeepSORT Tracker")
        
    def update(self, detections):
        """
        Update tracks with new detections
        
        Args:
            detections (list): List of detections [x1, y1, x2, y2, conf]
            
        Returns:
            list: List of tracking results [x1, y1, x2, y2, track_id]
        """
        # Increment age for all tracks
        for track_id in list(self.tracks.keys()):
            self.tracks[track_id]['age'] += 1
            
            # Remove old tracks
            if self.tracks[track_id]['age'] > self.max_age:
                del self.tracks[track_id]
                
        # Match detections with existing tracks
        matched_track_ids = []
        unmatched_detections = []
        
        for detection in detections:
            x1, y1, x2, y2 = detection[:4]
            best_iou = self.min_iou
            best_track_id = None
            
            for track_id, track in self.tracks.items():
                if track_id in matched_track_ids:
                    continue
                    
                tx1, ty1, tx2, ty2 = track['bbox']
                
                # Calculate IoU
                intersection_x1 = max(x1, tx1)
                intersection_y1 = max(y1, ty1)
                intersection_x2 = min(x2, tx2)
                intersection_y2 = min(y2, ty2)
                
                if intersection_x2 > intersection_x1 and intersection_y2 > intersection_y1:
                    intersection_area = (intersection_x2 - intersection_x1) * (intersection_y2 - intersection_y1)
                    detection_area = (x2 - x1) * (y2 - y1)
                    track_area = (tx2 - tx1) * (ty2 - ty1)
                    union_area = detection_area + track_area - intersection_area
                    iou = intersection_area / union_area
                    
                    if iou > best_iou:
                        best_iou = iou
                        best_track_id = track_id
                        
            if best_track_id is not None:
                # Update matched track
                self.tracks[best_track_id]['bbox'] = [x1, y1, x2, y2]
                self.tracks[best_track_id]['age'] = 0
                matched_track_ids.append(best_track_id)
            else:
                # New detection
                unmatched_detections.append(detection)
                
        # Create new tracks for unmatched detections
        for detection in unmatched_detections:
            x1, y1, x2, y2 = detection[:4]
            self.tracks[self.next_id] = {
                'bbox': [x1, y1, x2, y2],
                'age': 0
            }
            self.next_id += 1
            
        # Return current tracks
        results = []
        for track_id, track in self.tracks.items():
            if track['age'] <= self.max_age:  # Return all active tracks, not just newly updated ones
                x1, y1, x2, y2 = track['bbox']
                results.append([x1, y1, x2, y2, track_id])
        
        # Debug logging
        logger.debug(f"DeepSORT tracking results: {len(results)} tracks")
        return results


# Test the trackers if run directly
if __name__ == "__main__":
    import argparse
    from detector import PersonDetector
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Test person tracker')
    parser.add_argument('--video', type=str, required=True, help='Path to test video')
    parser.add_argument('--tracker', type=str, default='KCF', 
                        choices=['KCF', 'CSRT', 'MOSSE', 'MIL', 'BOOSTING', 'TLD', 'MEDIANFLOW', 'DeepSORT'],
                        help='Tracker type')
    parser.add_argument('--detect_interval', type=int, default=5, 
                        help='Run detection every N frames')
    args = parser.parse_args()
    
    # Initialize detector
    detector = PersonDetector()
    
    # Initialize tracker
    if args.tracker == 'DeepSORT':
        tracker = SimpleDeepSORT()
    else:
        tracker = OpenCVTracker(tracker_type=args.tracker)
    
    # Open video
    cap = cv2.VideoCapture(args.video)
    if not cap.isOpened():
        logger.error(f"Could not open video: {args.video}")
        exit(1)
        
    # Process video
    frame_count = 0
    fps_time = time.time()
    fps = 0
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
            
        frame_count += 1
        
        # Run detection at intervals
        if frame_count % args.detect_interval == 0:
            # Detect persons
            detections = detector.detect(frame)
            
            if frame_count == args.detect_interval:
                # Initialize tracker with first detections
                if args.tracker == 'DeepSORT':
                    tracking_results = tracker.update(detections)
                else:
                    tracker.init(frame, detections)
                    tracking_results = tracker.update(frame)
            else:
                # Update tracker with new detections
                if args.tracker == 'DeepSORT':
                    tracking_results = tracker.update(detections)
                else:
                    tracking_results = tracker.add_detections(frame, detections)
        else:
            # Just update tracker
            if args.tracker == 'DeepSORT':
                tracking_results = tracker.update([])
            else:
                tracking_results = tracker.update(frame)
                
        # Calculate FPS
        if frame_count % 10 == 0:
            fps = 10 / (time.time() - fps_time)
            fps_time = time.time()
            
        # Draw tracking results
        for result in tracking_results:
            x1, y1, x2, y2, track_id = result
            
            # Draw rectangle
            cv2.rectangle(frame, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 2)
            
            # Add label
            label = f"ID: {track_id}"
            cv2.putText(frame, label, (int(x1), int(y1) - 10), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
                        
        # Add FPS counter
        cv2.putText(frame, f"FPS: {fps:.1f}", (10, 30), 
                    cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
                    
        # Display frame
        cv2.imshow('Tracking', frame)
        
        # Break on 'q' key
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break
            
    cap.release()
    cv2.destroyAllWindows()