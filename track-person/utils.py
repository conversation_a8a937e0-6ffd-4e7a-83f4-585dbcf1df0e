import cv2
import numpy as np
import logging
import os
import time
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('Utils')

def calculate_iou(box1, box2):
    """
    Calculate IoU between two bounding boxes
    
    Args:
        box1 (list): First box in format [x1, y1, x2, y2]
        box2 (list): Second box in format [x1, y1, x2, y2]
        
    Returns:
        float: IoU value between 0 and 1
    """
    # Get coordinates of intersection
    x1_inter = max(box1[0], box2[0])
    y1_inter = max(box1[1], box2[1])
    x2_inter = min(box1[2], box2[2])
    y2_inter = min(box1[3], box2[3])
    
    # Calculate area of intersection
    width_inter = max(0, x2_inter - x1_inter)
    height_inter = max(0, y2_inter - y1_inter)
    area_inter = width_inter * height_inter
    
    # Calculate area of both boxes
    area_box1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
    area_box2 = (box2[2] - box2[0]) * (box2[3] - box2[1])
    
    # Calculate area of union
    area_union = area_box1 + area_box2 - area_inter
    
    # Calculate IoU
    iou = area_inter / area_union if area_union > 0 else 0
    
    return iou

def xywh_to_xyxy(box):
    """
    Convert bounding box from [x, y, width, height] to [x1, y1, x2, y2] format
    
    Args:
        box (list): Box in format [x, y, width, height]
        
    Returns:
        list: Box in format [x1, y1, x2, y2]
    """
    x, y, w, h = box
    return [x, y, x + w, y + h]

def xyxy_to_xywh(box):
    """
    Convert bounding box from [x1, y1, x2, y2] to [x, y, width, height] format
    
    Args:
        box (list): Box in format [x1, y1, x2, y2]
        
    Returns:
        list: Box in format [x, y, width, height]
    """
    x1, y1, x2, y2 = box
    return [x1, y1, x2 - x1, y2 - y1]

def draw_tracks(frame, tracks, show_ids=True, color=None):
    """
    Draw tracking results on frame
    
    Args:
        frame (numpy.ndarray): Input frame
        tracks (list): List of tracks in format [x1, y1, x2, y2, track_id]
        show_ids (bool): Whether to show track IDs
        color (tuple): Color for bounding boxes (B, G, R)
        
    Returns:
        numpy.ndarray: Frame with tracks drawn
    """
    # Create a copy of the frame
    result = frame.copy()
    
    # Draw each track
    for track in tracks:
        x1, y1, x2, y2 = map(int, track[:4])
        track_id = track[4]
        
        # Use different colors for different tracks if color not specified
        if color is None:
            # Generate a color based on track ID
            track_color = ((track_id * 123) % 255, (track_id * 85) % 255, (track_id * 37) % 255)
        else:
            track_color = color
        
        # Draw bounding box
        cv2.rectangle(result, (x1, y1), (x2, y2), track_color, 2)
        
        # Draw ID if requested
        if show_ids:
            label = f"ID: {track_id}"
            cv2.putText(result, label, (x1, y1 - 10), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, track_color, 2)
    
    return result

def create_video_writer(video_path, output_path, fps=None, size=None):
    """
    Create a video writer based on input video properties
    
    Args:
        video_path (str): Path to input video
        output_path (str): Path to output video
        fps (float): Frames per second (if None, use input video fps)
        size (tuple): Frame size (width, height) (if None, use input video size)
        
    Returns:
        cv2.VideoWriter: Video writer object
    """
    # Create output directory if it doesn't exist
    os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)
    
    # Open input video to get properties
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        logger.error(f"Could not open video: {video_path}")
        return None
    
    # Get video properties if not specified
    if fps is None:
        fps = cap.get(cv2.CAP_PROP_FPS)
    
    if size is None:
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        size = (width, height)
    
    # Release input video
    cap.release()
    
    # Create video writer
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    writer = cv2.VideoWriter(output_path, fourcc, fps, size)
    
    return writer

def save_tracks_to_file(tracks_history, output_path):
    """
    Save tracking results to a CSV file
    
    Args:
        tracks_history (dict): Dictionary of frame_number -> list of tracks
        output_path (str): Path to output CSV file
    """
    # Create output directory if it doesn't exist
    os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)
    
    # Open file for writing
    with open(output_path, 'w') as f:
        # Write header
        f.write("frame,track_id,x1,y1,x2,y2\n")
        
        # Write data
        for frame_number, tracks in sorted(tracks_history.items()):
            for track in tracks:
                x1, y1, x2, y2, track_id = track
                f.write(f"{frame_number},{track_id},{x1},{y1},{x2},{y2}\n")
    
    logger.info(f"Saved tracking results to {output_path}")

def count_tracks_in_area(tracks, area_polygon):
    """
    Count number of tracks in a specified area
    
    Args:
        tracks (list): List of tracks in format [x1, y1, x2, y2, track_id]
        area_polygon (list): List of points defining the area [(x1, y1), (x2, y2), ...]
        
    Returns:
        int: Number of tracks in the area
    """
    count = 0
    
    # Convert polygon to numpy array
    polygon = np.array(area_polygon, dtype=np.int32)
    
    for track in tracks:
        x1, y1, x2, y2 = map(int, track[:4])
        
        # Use center point of bounding box
        center_x = (x1 + x2) // 2
        center_y = (y1 + y2) // 2
        
        # Check if point is inside polygon
        if cv2.pointPolygonTest(polygon, (center_x, center_y), False) >= 0:
            count += 1
    
    return count

def draw_area(frame, area_polygon, color=(0, 0, 255), thickness=2):
    """
    Draw an area polygon on the frame
    
    Args:
        frame (numpy.ndarray): Input frame
        area_polygon (list): List of points defining the area [(x1, y1), (x2, y2), ...]
        color (tuple): Color for polygon (B, G, R)
        thickness (int): Line thickness
        
    Returns:
        numpy.ndarray: Frame with area drawn
    """
    # Create a copy of the frame
    result = frame.copy()
    
    # Convert polygon to numpy array
    polygon = np.array(area_polygon, dtype=np.int32)
    
    # Draw polygon
    cv2.polylines(result, [polygon], True, color, thickness)
    
    return result

def measure_fps(func):
    """
    Decorator to measure function execution time and FPS
    
    Args:
        func: Function to measure
        
    Returns:
        wrapper: Wrapped function
    """
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        execution_time = end_time - start_time
        fps = 1 / execution_time if execution_time > 0 else 0
        
        logger.debug(f"{func.__name__} executed in {execution_time:.3f}s ({fps:.1f} FPS)")
        
        return result
    
    return wrapper

def get_timestamp():
    """
    Get current timestamp string
    
    Returns:
        str: Timestamp string in format YYYY-MM-DD_HH-MM-SS
    """
    return datetime.now().strftime("%Y-%m-%d_%H-%M-%S")

def resize_frame(frame, width=None, height=None):
    """
    Resize frame while maintaining aspect ratio
    
    Args:
        frame (numpy.ndarray): Input frame
        width (int): Target width (if None, calculate from height)
        height (int): Target height (if None, calculate from width)
        
    Returns:
        numpy.ndarray: Resized frame
    """
    # Get original dimensions
    h, w = frame.shape[:2]
    
    # If both width and height are None, return original frame
    if width is None and height is None:
        return frame
    
    # Calculate new dimensions
    if width is None:
        # Calculate width to maintain aspect ratio
        aspect_ratio = w / h
        width = int(height * aspect_ratio)
    elif height is None:
        # Calculate height to maintain aspect ratio
        aspect_ratio = h / w
        height = int(width * aspect_ratio)
    
    # Resize frame
    resized = cv2.resize(frame, (width, height))
    
    return resized