import cv2
import numpy as np
import logging
import time
import os
from detector import PersonDete<PERSON>
from tracker import OpenCVTracker, SimpleDeepSORT

# Configure logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('TestTracking')

def test_tracking(video_path=None, tracker_type="KCF", max_frames=100):
    """
    Test the tracking functionality with a video or webcam
    
    Args:
        video_path (str): Path to video file (None for webcam)
        tracker_type (str): Type of tracker to use
        max_frames (int): Maximum number of frames to process
    """
    # Initialize detector
    logger.info("Initializing detector...")
    detector = PersonDetector()
    
    # Initialize tracker
    logger.info(f"Initializing {tracker_type} tracker...")
    if tracker_type == "DeepSORT":
        tracker = SimpleDeepSORT()
    else:
        tracker = OpenCVTracker(tracker_type=tracker_type)
    
    # Open video or webcam
    if video_path:
        logger.info(f"Opening video: {video_path}")
        cap = cv2.VideoCapture(video_path)
    else:
        logger.info("Opening webcam")
        cap = cv2.VideoCapture(0)
        
    if not cap.isOpened():
        logger.error("Could not open video source")
        return
    
    # Process frames
    frame_count = 0
    tracking_initialized = False
    
    while frame_count < max_frames:
        ret, frame = cap.read()
        if not ret:
            logger.info("End of video")
            break
        
        frame_count += 1
        logger.info(f"Processing frame {frame_count}")
        
        # Run detection every 5 frames or on first frame
        if frame_count % 5 == 0 or frame_count == 1:
            logger.info("Running detection")
            detections = detector.detect(frame)
            logger.info(f"Detected {len(detections)} persons")
            
            # Initialize or update tracker with detections
            if not tracking_initialized:
                if tracker_type == "DeepSORT":
                    tracking_results = tracker.update(detections)
                else:
                    tracker.init(frame, detections)
                    tracking_results = tracker.update(frame)
                tracking_initialized = True
                logger.info(f"Tracker initialized with {len(tracking_results)} tracks")
            else:
                if tracker_type == "DeepSORT":
                    tracking_results = tracker.update(detections)
                else:
                    tracking_results = tracker.add_detections(frame, detections)
                logger.info(f"Tracker updated with detections: {len(tracking_results)} tracks")
        else:
            # Just update tracker
            if tracker_type == "DeepSORT":
                tracking_results = tracker.update([])
            else:
                tracking_results = tracker.update(frame)
            logger.info(f"Tracker updated: {len(tracking_results)} tracks")
        
        # Draw tracking results
        for result in tracking_results:
            x1, y1, x2, y2, track_id = result
            
            # Draw rectangle
            cv2.rectangle(frame, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 2)
            
            # Add label
            label = f"ID: {track_id}"
            cv2.putText(frame, label, (int(x1), int(y1) - 10), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
        
        # Add frame info
        cv2.putText(frame, f"Frame: {frame_count}", (10, 30), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        cv2.putText(frame, f"Tracks: {len(tracking_results)}", (10, 60), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        
        # Display frame
        cv2.imshow('Tracking Test', frame)
        
        # Break on 'q' key
        if cv2.waitKey(30) & 0xFF == ord('q'):
            break
    
    # Clean up
    cap.release()
    cv2.destroyAllWindows()
    logger.info("Test completed")

if __name__ == "__main__":
    import argparse
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Test person tracking')
    parser.add_argument('--video', type=str, help='Path to video file (optional)')
    parser.add_argument('--tracker', type=str, default='KCF', 
                        choices=['KCF', 'CSRT', 'MOSSE', 'MIL', 'BOOSTING', 'TLD', 'MEDIANFLOW', 'DeepSORT'],
                        help='Tracker type')
    parser.add_argument('--frames', type=int, default=100, help='Maximum frames to process')
    args = parser.parse_args()
    
    # Run test
    test_tracking(args.video, args.tracker, args.frames)