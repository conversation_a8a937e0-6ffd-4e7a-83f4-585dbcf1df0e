#!/bin/bash

# Create output directory if it doesn't exist
mkdir -p ../output

# Define the parameter values to test
MAX_AGE_VALUES=(200)
MIN_IOU_VALUES=(0.2)

# Check if video file exists
if [ ! -f "/home/<USER>/vanhc/smart-camera/video/nhamaytamphuoc_0304_10p.mp4" ]; then
    echo "Error: Video file not found at /home/<USER>/vanhc/smart-camera/video/nhamaytamphuoc_0304_10p.mp4"
    exit 1
fi

# Run experiments with different parameter combinations
for max_age in "${MAX_AGE_VALUES[@]}"; do
    for min_iou in "${MIN_IOU_VALUES[@]}"; do
        echo "Running with MAX_AGE=$max_age, MIN_IOU=$min_iou"
        
        # Set environment variables and run the command
        export MAX_AGE=$max_age
        export MIN_IOU=$min_iou
        
        output_file="../output/deepsort_${max_age}_${min_iou}.mp4"
        
        echo "Command: python3 main.py --video /home/<USER>/vanhc/smart-camera/video/nhamaytamphuoc_0304_10p.mp4 --tracker DeepSORT --log_level INFO --no_display --output $output_file --detect_interval 25"
        
        start_time=$(date +%s)
        python3 track-person/main.py --video /home/<USER>/vanhc/smart-camera/video/nhamaytamphuoc_0304_10p.mp4 --tracker DeepSORT --log_level INFO --no_display --output "$output_file" --detect_interval 2
        end_time=$(date +%s)
        execution_time=$((end_time - start_time))
        echo "Execution time: $execution_time seconds"
        
        # Check if the command was successful
        if [ $? -eq 0 ]; then
            echo "Successfully completed experiment with MAX_AGE=$max_age, MIN_IOU=$min_iou"
            echo "Output saved to $output_file"
        else
            echo "Error running experiment with MAX_AGE=$max_age, MIN_IOU=$min_iou"
        fi
        
        echo "----------------------------------------"
    done
done

echo "All experiments completed!"