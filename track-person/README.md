# Person Tracking with YOLO and OpenCV

This project implements a person tracking system using YOLO for detection and OpenCV for tracking. It provides two tracking approaches:

1. **OpenCV Trackers** - Using built-in OpenCV trackers (KCF, CSRT, MOSSE, etc.)
2. **Simple DeepSORT** - A simplified implementation of DeepSORT tracking

## Features

- Person detection using YOLOv8
- Multiple tracking algorithms
- Performance logging and visualization
- Support for video files and webcam input
- Track visualization and ID assignment
- Utility functions for tracking analysis

## Requirements

Install the required dependencies:

```bash
pip install -r requirements.txt
```

## Project Structure

- `detector.py` - YOLO-based person detector
- `tracker.py` - Implementation of tracking algorithms
- `main.py` - Main script to run the application
- `utils.py` - Utility functions for tracking and visualization

## Usage

### Process a Video File

```bash
python main.py --video path/to/video.mp4 --tracker KCF --output path/to/output.mp4
```

### Process Webcam Feed

```bash
python main.py --webcam 0 --tracker CSRT
```

### Command Line Arguments

- `--video` - Path to input video file
- `--webcam` - Webcam device ID (default: 0)
- `--tracker` - Tracker type (KCF, CSRT, MOSSE, MIL, BOOSTING, TLD, MEDIANFLOW, DeepSORT)
- `--detect_interval` - Run detection every N frames (default: 5)
- `--output` - Path to save output video
- `--no_display` - Do not display video
- `--log_level` - Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)

## Tracker Types

The system supports multiple tracker types:

- **KCF** - Kernelized Correlation Filter (fast and accurate)
- **CSRT** - Discriminative Correlation Filter with Channel and Spatial Reliability (more accurate but slower)
- **MOSSE** - Minimum Output Sum of Squared Error (extremely fast but less accurate)
- **MIL** - Multiple Instance Learning
- **BOOSTING** - Based on AdaBoost algorithm
- **TLD** - Tracking, Learning and Detection
- **MEDIANFLOW** - Forward-backward error tracking
- **DeepSORT** - Simplified implementation of DeepSORT (without Kalman filter)

## Performance Considerations

- Detection is computationally expensive, so it's run at intervals (every N frames)
- Tracking is faster but can drift over time, so periodic detection helps correct drift
- More trackers will decrease performance
- CSRT is more accurate but slower, KCF offers a good balance, MOSSE is fastest

## Debugging

To enable debug logging:

```bash
python main.py --video path/to/video.mp4 --log_level DEBUG
```

## Examples

### Track persons in a video file using KCF tracker

```bash
python main.py --video samples/people.mp4 --tracker KCF --output results/tracked.mp4
```

### Track persons from webcam using CSRT tracker

```bash
python main.py --webcam 0 --tracker CSRT
```

### Track persons with DeepSORT

```bash
python main.py --video samples/people.mp4 --tracker DeepSORT
```

## References

- [PyImageSearch - Tracking multiple objects with OpenCV](https://pyimagesearch.com/2018/08/06/tracking-multiple-objects-with-opencv/)
- [Ultralytics YOLOv8](https://github.com/ultralytics/ultralytics)
- [Simple Online and Realtime Tracking with a Deep Association Metric (DeepSORT)](https://arxiv.org/abs/1703.07402)