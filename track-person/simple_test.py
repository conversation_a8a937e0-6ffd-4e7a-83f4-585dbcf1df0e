import cv2
import logging
from detector import PersonDetector

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger('SimpleTest')

def test_detector():
    """Simple test for the detector"""
    # Initialize detector
    logger.info("Initializing detector...")
    detector = PersonDetector()
    logger.info("Detector initialized successfully")
    
    return True

if __name__ == "__main__":
    test_detector()
    logger.info("Test completed successfully")