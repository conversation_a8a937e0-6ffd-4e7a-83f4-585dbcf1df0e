import cv2
import numpy as np
import logging
import os
import time
from tracker import OpenCVTracker, SimpleDeepSORT

# Configure logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('TestTracker')

def create_sample_image(width=640, height=480):
    """Create a sample image"""
    return np.zeros((height, width, 3), dtype=np.uint8)

def simulate_detections(frame_number):
    """
    Simulate person detections
    
    Args:
        frame_number (int): Current frame number
        
    Returns:
        list: List of simulated detections [x1, y1, x2, y2, conf]
    """
    detections = []
    
    # Simulate a person moving from left to right
    x_pos = 100 + frame_number * 5
    if x_pos < 500:  # Keep person in frame
        detections.append([x_pos, 100, x_pos + 50, 300, 0.9])
    
    # Add a second person in later frames
    if frame_number > 10 and frame_number < 30:
        detections.append([300, 200, 350, 400, 0.85])
    
    return detections

def test_tracker(tracker_type="KCF", num_frames=30):
    """
    Test the tracker with simulated detections
    
    Args:
        tracker_type (str): Type of tracker to use
        num_frames (int): Number of frames to simulate
    """
    # Initialize tracker
    logger.info(f"Initializing {tracker_type} tracker...")
    if tracker_type == "DeepSORT":
        tracker = SimpleDeepSORT()
    else:
        tracker = OpenCVTracker(tracker_type=tracker_type)
    
    # Create output directory
    output_dir = "tracker_test_output"
    os.makedirs(output_dir, exist_ok=True)
    
    # Process simulated frames
    tracking_results_history = []
    
    for frame_number in range(1, num_frames + 1):
        logger.info(f"Processing frame {frame_number}")
        
        # Create a blank frame
        frame = create_sample_image()
        
        # Get simulated detections
        detections = simulate_detections(frame_number)
        logger.info(f"Simulated {len(detections)} detections")
        
        # Initialize or update tracker
        if frame_number == 1:
            # Initialize tracker with first detections
            if tracker_type == "DeepSORT":
                tracking_results = tracker.update(detections)
            else:
                tracker.init(frame, detections)
                tracking_results = tracker.update(frame)
            logger.info(f"Tracker initialized with {len(tracking_results)} tracks")
        elif frame_number % 5 == 0:  # Update with detections every 5 frames
            # Update tracker with new detections
            if tracker_type == "DeepSORT":
                tracking_results = tracker.update(detections)
            else:
                tracking_results = tracker.add_detections(frame, detections)
            logger.info(f"Tracker updated with detections: {len(tracking_results)} tracks")
        else:
            # Just update tracker
            if tracker_type == "DeepSORT":
                tracking_results = tracker.update([])
            else:
                tracking_results = tracker.update(frame)
            logger.info(f"Tracker updated: {len(tracking_results)} tracks")
        
        # Store tracking results
        tracking_results_history.append(tracking_results)
        
        # Draw detections and tracking results
        # Draw detections in red
        for x1, y1, x2, y2, conf in detections:
            cv2.rectangle(frame, (int(x1), int(y1)), (int(x2), int(y2)), (0, 0, 255), 2)
            label = f"Det: {conf:.2f}"
            cv2.putText(frame, label, (int(x1), int(y1) - 10), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)
        
        # Draw tracking results in green
        for result in tracking_results:
            x1, y1, x2, y2, track_id = result
            cv2.rectangle(frame, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 2)
            label = f"ID: {track_id}"
            cv2.putText(frame, label, (int(x1), int(y1) - 10), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
        
        # Add frame info
        cv2.putText(frame, f"Frame: {frame_number}", (10, 30), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        cv2.putText(frame, f"Detections: {len(detections)}", (10, 60), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        cv2.putText(frame, f"Tracks: {len(tracking_results)}", (10, 90), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        # Save frame
        output_path = os.path.join(output_dir, f"frame_{frame_number:03d}.jpg")
        cv2.imwrite(output_path, frame)
    
    # Analyze tracking results
    logger.info("Tracking Results Analysis:")
    for frame_number, results in enumerate(tracking_results_history, 1):
        logger.info(f"Frame {frame_number}: {len(results)} tracks")
        for result in results:
            logger.info(f"  Track ID {result[4]}: {result[:4]}")
    
    logger.info(f"Test completed. Output saved to {output_dir}")
    return tracking_results_history

if __name__ == "__main__":
    import argparse
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Test person tracker')
    parser.add_argument('--tracker', type=str, default='KCF', 
                        choices=['KCF', 'CSRT', 'MOSSE', 'MIL', 'BOOSTING', 'TLD', 'MEDIANFLOW', 'DeepSORT'],
                        help='Tracker type')
    parser.add_argument('--frames', type=int, default=30, help='Number of frames to simulate')
    args = parser.parse_args()
    
    # Run test
    test_tracker(args.tracker, args.frames)