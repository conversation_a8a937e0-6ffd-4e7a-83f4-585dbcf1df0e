import cv2
import numpy as np
import torch
import logging
import os
from ultralytics import YOLO

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('PersonDetector')

class PersonDetector:
    """
    Class for detecting persons in images using YOLO
    """
    def __init__(self, model_path=None, conf_threshold=0.4, device=None):
        """
        Initialize the person detector
        
        Args:
            model_path (str): Path to YOLO model file. If None, will use YOLOv8n
            conf_threshold (float): Confidence threshold for detections
            device (str): Device to run inference on ('cpu', 'cuda', etc.)
        """
        self.conf_threshold = conf_threshold
        
        # Set device
        if device is None:
            self.device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = device
            
        logger.info(f"Using device: {self.device}")
        
        # Load YOLO model
        try:
            if model_path and os.path.exists(model_path):
                logger.info(f"Loading YOLO model from {model_path}")
                self.model = YOLO(model_path)
            else:
                logger.info("Loading default YOLOv8l model")
                self.model = YOLO('yolov8l.pt')
                
            logger.info("YOLO model loaded successfully")
        except Exception as e:
            logger.error(f"Error loading YOLO model: {e}")
            raise
            
    def detect(self, frame):
        """
        Detect persons in a frame
        
        Args:
            frame (numpy.ndarray): Input frame (BGR format)
            
        Returns:
            list: List of detections, each containing [x1, y1, x2, y2, confidence]
        """
        try:
            # Run YOLO detection
            results = self.model(frame, verbose=False)
            
            # Extract person detections (class 0 in COCO dataset)
            detections = []
            
            for result in results:
                boxes = result.boxes
                
                for i, box in enumerate(boxes):
                    cls = int(box.cls.item())
                    conf = box.conf.item()
                    
                    # Only keep person detections (class 0) above threshold
                    if cls == 0 and conf >= self.conf_threshold:
                        x1, y1, x2, y2 = map(int, box.xyxy[0].tolist())
                        detections.append([x1, y1, x2, y2, conf])
            
            logger.debug(f"Detected {len(detections)} persons")
            return detections
            
        except Exception as e:
            logger.error(f"Error during detection: {e}")
            return []
            
    def detect_with_visualization(self, frame):
        """
        Detect persons and draw bounding boxes on the frame
        
        Args:
            frame (numpy.ndarray): Input frame (BGR format)
            
        Returns:
            tuple: (annotated_frame, detections)
        """
        # Get detections
        detections = self.detect(frame)
        
        # Create a copy of the frame for drawing
        annotated_frame = frame.copy()
        
        # Draw bounding boxes
        for x1, y1, x2, y2, conf in detections:
            # Draw rectangle
            cv2.rectangle(annotated_frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
            
            # Add label
            label = f"Person: {conf:.2f}"
            cv2.putText(annotated_frame, label, (x1, y1 - 10), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
        
        return annotated_frame, detections


# Test the detector if run directly
if __name__ == "__main__":
    import argparse
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Test person detector')
    parser.add_argument('--image', type=str, help='Path to test image')
    parser.add_argument('--video', type=str, help='Path to test video')
    parser.add_argument('--model', type=str, default=None, help='Path to YOLO model')
    parser.add_argument('--conf', type=float, default=0.4, help='Confidence threshold')
    args = parser.parse_args()
    
    # Initialize detector
    detector = PersonDetector(model_path=args.model, conf_threshold=args.conf)
    
    if args.image:
        # Test on image
        image = cv2.imread(args.image)
        if image is None:
            logger.error(f"Could not read image: {args.image}")
            exit(1)
            
        annotated_image, detections = detector.detect_with_visualization(image)
        
        # Display results
        logger.info(f"Detected {len(detections)} persons")
        cv2.imshow('Detections', annotated_image)
        cv2.waitKey(0)
        cv2.destroyAllWindows()
        
    elif args.video:
        # Test on video
        cap = cv2.VideoCapture(args.video)
        if not cap.isOpened():
            logger.error(f"Could not open video: {args.video}")
            exit(1)
            
        while True:
            ret, frame = cap.read()
            if not ret:
                break
                
            annotated_frame, detections = detector.detect_with_visualization(frame)
            
            # Display results
            cv2.imshow('Detections', annotated_frame)
            
            # Break on 'q' key
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
                
        cap.release()
        cv2.destroyAllWindows()
        
    else:
        logger.error("Please provide either --image or --video argument")