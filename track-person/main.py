import cv2
import numpy as np
import argparse
import logging
import time
import os
from detector import PersonDetector
from tracker import OpenCVTracker, SimpleDeepSORT

MAX_AGE = int(os.getenv('MAX_AGE','100'))
MIN_IOU = float(os.getenv('MIN_IOU','0.3'))
# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('PersonTrackingSystem')

def process_video(video_path, output_path=None, tracker_type="KCF", detect_interval=5, 
                  show_video=True, save_video=False, log_performance=True):
    """
    Process a video file for person tracking
    
    Args:
        video_path (str): Path to input video
        output_path (str): Path to save output video (if save_video is True)
        tracker_type (str): Type of tracker to use
        detect_interval (int): Run detection every N frames
        show_video (bool): Whether to display the video
        save_video (bool): Whether to save the output video
        log_performance (bool): Whether to log performance metrics
    """
    # Initialize detector
    logger.info("Initializing person detector...")
    detector = PersonDetector()
    
    # Initialize tracker
    logger.info(f"Initializing {tracker_type} tracker...")
    if tracker_type == "DeepSORT":
        tracker = SimpleDeepSORT(max_age=MAX_AGE, min_iou=MIN_IOU)
    else:
        tracker = OpenCVTracker(tracker_type=tracker_type)
    
    # Open video
    logger.info(f"Opening video: {video_path}")
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        logger.error(f"Could not open video: {video_path}")
        return
        
    # Get video properties
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fps = cap.get(cv2.CAP_PROP_FPS)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    logger.info(f"Video properties: {width}x{height}, {fps} FPS, {total_frames} frames")
    
    # Initialize video writer if saving
    video_writer = None
    if save_video and output_path:
        os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        video_writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
        logger.info(f"Saving output to: {output_path}")
    else:
        # Create a default output path if none provided
        output_path = f"output_{int(time.time())}.mp4"
        os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        video_writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
        logger.info(f"No output path provided, saving to: {output_path}")
        save_video = True
    
    # Process video
    frame_count = 0
    processing_times = []
    detection_times = []
    tracking_times = []
    
    # Disable display in headless environment
    show_video = show_video and os.environ.get('DISPLAY') is not None
    if not show_video:
        logger.info("Display not available, running in headless mode")
    
    logger.info("Starting video processing...")
    
    while True:
        # Read frame
        start_time = time.time()
        ret, frame = cap.read()
        if not ret:
            break
            
        frame_count += 1
        logger.debug(f"Processing frame {frame_count}/{total_frames}")
        
        # Run detection at intervals
        if frame_count % detect_interval == 0:
            # Detect persons
            detect_start = time.time()
            detections = detector.detect(frame)
            detect_end = time.time()
            detection_times.append(detect_end - detect_start)
            
            # Log detection results
            logger.debug(f"Detected {len(detections)} persons in {detect_end - detect_start:.3f}s")
            
            # Initialize or update tracker with new detections
            track_start = time.time()
            if frame_count == detect_interval:
                # Initialize tracker with first detections
                if tracker_type == "DeepSORT":
                    tracking_results = tracker.update(detections)
                else:
                    tracker.init(frame, detections)
                    tracking_results = tracker.update(frame)
            else:
                # Update tracker with new detections
                if tracker_type == "DeepSORT":
                    tracking_results = tracker.update(detections)
                else:
                    tracking_results = tracker.add_detections(frame, detections)
            track_end = time.time()
            tracking_times.append(track_end - track_start)
            
            # Log tracking results after detection
            logger.debug(f"Tracking results after detection: {len(tracking_results)} tracks")
        else:
            # Just update tracker
            track_start = time.time()
            if tracker_type == "DeepSORT":
                tracking_results = tracker.update([])
            else:
                tracking_results = tracker.update(frame)
            track_end = time.time()
            tracking_times.append(track_end - track_start)
            
            # Log tracking results after update
            logger.debug(f"Tracking results after update: {len(tracking_results)} tracks")
                
        # Draw tracking results
        logger.debug(f"Drawing {len(tracking_results)} tracking results")
        for result in tracking_results:
            x1, y1, x2, y2, track_id = result
            
            # Draw rectangle
            cv2.rectangle(frame, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 2)
            
            # Add label
            label = f"ID: {track_id}"
            cv2.putText(frame, label, (int(x1), int(y1) - 10), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
                        
        # Add frame info
        cv2.putText(frame, f"Frame: {frame_count}/{total_frames}", (10, 30), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                    
        # Calculate and display FPS (based on total processing time)
        processing_time = time.time() - start_time
        processing_times.append(processing_time)
        
        fps_text = f"FPS: {1/processing_time:.1f}"
        cv2.putText(frame, fps_text, (10, 60), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                    
        # Display frame if available
        if show_video:
            try:
                cv2.imshow('Person Tracking', frame)
                
                # Break on 'q' key
                if cv2.waitKey(1) & 0xFF == ord('q'):
                    break
            except Exception as e:
                logger.warning(f"Could not display frame: {e}")
                show_video = False
                
        # Save frame
        if video_writer:
            video_writer.write(frame)
            
        # Save individual frames for debugging
        if log_performance and frame_count % 10 == 0:
            debug_dir = os.path.join(os.path.dirname(output_path), 'debug_frames')
            os.makedirs(debug_dir, exist_ok=True)
            debug_path = os.path.join(debug_dir, f"frame_{frame_count:04d}.jpg")
            cv2.imwrite(debug_path, frame)
            logger.debug(f"Saved debug frame to {debug_path}")
            
        # Log progress periodically
        if frame_count % 100 == 0 or frame_count == total_frames:
            progress = frame_count / total_frames * 100
            logger.info(f"Progress: {progress:.1f}% ({frame_count}/{total_frames})")
            
            if log_performance and len(processing_times) > 0:
                avg_fps = 1 / np.mean(processing_times[-100:])
                logger.info(f"Average FPS: {avg_fps:.1f}")
    
    # Clean up
    cap.release()
    if video_writer:
        video_writer.release()
    cv2.destroyAllWindows()
    
    # Log performance statistics
    if log_performance and len(processing_times) > 0:
        avg_processing_time = np.mean(processing_times)
        avg_detection_time = np.mean(detection_times) if detection_times else 0
        avg_tracking_time = np.mean(tracking_times) if tracking_times else 0
        
        logger.info("Performance Statistics:")
        logger.info(f"  Average processing time: {avg_processing_time:.3f}s ({1/avg_processing_time:.1f} FPS)")
        logger.info(f"  Average detection time: {avg_detection_time:.3f}s")
        logger.info(f"  Average tracking time: {avg_tracking_time:.3f}s")
        logger.info(f"  Total frames processed: {frame_count}")
        
    logger.info("Video processing complete")


def process_webcam(camera_id=0, tracker_type="KCF", detect_interval=5):
    """
    Process webcam feed for person tracking
    
    Args:
        camera_id (int): Camera ID
        tracker_type (str): Type of tracker to use
        detect_interval (int): Run detection every N frames
    """
    # Initialize detector
    logger.info("Initializing person detector...")
    detector = PersonDetector()
    
    # Initialize tracker
    logger.info(f"Initializing {tracker_type} tracker...")
    if tracker_type == "DeepSORT":
        tracker = SimpleDeepSORT(max_age=MAX_AGE, min_iou=MIN_IOU)
    else:
        tracker = OpenCVTracker(tracker_type=tracker_type)
    
    # Open webcam
    logger.info(f"Opening webcam: {camera_id}")
    cap = cv2.VideoCapture(camera_id)
    if not cap.isOpened():
        logger.error(f"Could not open webcam: {camera_id}")
        return
        
    # Process video
    frame_count = 0
    fps_time = time.time()
    fps = 0
    
    logger.info("Starting webcam processing...")
    
    while True:
        # Read frame
        ret, frame = cap.read()
        if not ret:
            break
            
        frame_count += 1
        
        # Run detection at intervals
        if frame_count % detect_interval == 0:
            # Detect persons
            detections = detector.detect(frame)
            
            # Initialize or update tracker with new detections
            if frame_count == detect_interval:
                # Initialize tracker with first detections
                if tracker_type == "DeepSORT":
                    tracking_results = tracker.update(detections)
                else:
                    tracker.init(frame, detections)
                    tracking_results = tracker.update(frame)
            else:
                # Update tracker with new detections
                if tracker_type == "DeepSORT":
                    tracking_results = tracker.update(detections)
                else:
                    tracking_results = tracker.add_detections(frame, detections)
        else:
            # Just update tracker
            if tracker_type == "DeepSORT":
                tracking_results = tracker.update([])
            else:
                tracking_results = tracker.update(frame)
                
        # Calculate FPS
        if frame_count % 10 == 0:
            fps = 10 / (time.time() - fps_time)
            fps_time = time.time()
            
        # Draw tracking results
        for result in tracking_results:
            x1, y1, x2, y2, track_id = result
            
            # Draw rectangle
            cv2.rectangle(frame, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 2)
            
            # Add label
            label = f"ID: {track_id}"
            cv2.putText(frame, label, (int(x1), int(y1) - 10), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
                        
        # Add FPS counter
        cv2.putText(frame, f"FPS: {fps:.1f}", (10, 30), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                    
        # Display frame
        cv2.imshow('Person Tracking', frame)
        
        # Break on 'q' key
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break
            
    # Clean up
    cap.release()
    cv2.destroyAllWindows()
    logger.info("Webcam processing complete")


if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Person tracking with YOLO and OpenCV')
    
    # Input source
    input_group = parser.add_mutually_exclusive_group(required=True)
    input_group.add_argument('--video', type=str, help='Path to input video file')
    input_group.add_argument('--webcam', type=int, default=0, help='Webcam device ID')
    
    # Tracker options
    parser.add_argument('--tracker', type=str, default='KCF', 
                        choices=['KCF', 'CSRT', 'MOSSE', 'MIL', 'BOOSTING', 'TLD', 'MEDIANFLOW', 'DeepSORT'],
                        help='Tracker type')
    parser.add_argument('--detect_interval', type=int, default=5, 
                        help='Run detection every N frames')
    
    # Output options
    parser.add_argument('--output', type=str, help='Path to save output video')
    parser.add_argument('--no_display', action='store_true', help='Do not display video')
    parser.add_argument('--log_level', type=str, default='INFO',
                        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
                        help='Logging level')
    
    args = parser.parse_args()
    
    # Set logging level
    logging.getLogger().setLevel(getattr(logging, args.log_level))
    
    # Process video or webcam
    if args.video:
         process_video(
            video_path=args.video,
            output_path=args.output,
            tracker_type=args.tracker,
            detect_interval=args.detect_interval,
            show_video=not args.no_display,
            save_video=args.output is not None
        )
    else:
        process_webcam(
            camera_id=args.webcam,
            tracker_type=args.tracker,
            detect_interval=args.detect_interval
        )