"""
GPU-optimized YOL<PERSON> detector for person detection
"""
import cv2
import numpy as np
import torch
import logging
from typing import List, Tuple, Optional
from ultralytics import Y<PERSON><PERSON>
try:
    from .config import DetectionConfig
    from .performance_monitor import Timer
except ImportError:
    # Handle case when running as script
    from config import DetectionConfig
    from performance_monitor import Timer


logger = logging.getLogger(__name__)


class Detection:
    """Detection result container"""

    def __init__(self, bbox: List[float], confidence: float, class_id: int = 0):
        self.bbox = bbox  # [x1, y1, x2, y2]
        self.confidence = confidence
        self.class_id = class_id
        self.feature = None  # Will be filled by feature extractor

    @property
    def tlwh(self) -> List[float]:
        """Get bounding box in top-left-width-height format"""
        x1, y1, x2, y2 = self.bbox
        return [x1, y1, x2 - x1, y2 - y1]

    @property
    def center(self) -> Tuple[float, float]:
        """Get center point of bounding box"""
        x1, y1, x2, y2 = self.bbox
        return ((x1 + x2) / 2, (y1 + y2) / 2)

    @property
    def area(self) -> float:
        """Get area of bounding box"""
        x1, y1, x2, y2 = self.bbox
        return (x2 - x1) * (y2 - y1)


class YOLODetector:
    """GPU-optimized YOLO detector for person detection"""

    def __init__(self, config: DetectionConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)

        # Initialize YOLO model
        self.model = None
        self.device = None
        self._initialize_model()

        # Performance tracking
        self.total_detections = 0
        self.total_inference_time = 0.0

    def _initialize_model(self):
        """Initialize YOLO model with GPU optimization"""
        try:
            self.logger.info(f"Loading YOLO model: {self.config.model_path}")
            self.model = YOLO(self.config.model_path)

            # Set device
            if self.config.device == "cuda" and torch.cuda.is_available():
                self.device = "cuda"
                self.logger.info(f"Using GPU: {torch.cuda.get_device_name()}")

                # Optimize for GPU
                self.model.to(self.device)

                # Warm up the model
                self._warmup_model()
            else:
                self.device = "cpu"
                self.logger.info("Using CPU for detection")

            self.logger.info("YOLO model initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize YOLO model: {e}")
            raise

    def _warmup_model(self, warmup_iterations: int = 3):
        """Warm up the model for optimal GPU performance"""
        self.logger.info("Warming up YOLO model...")

        # Create dummy input
        dummy_input = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)

        for i in range(warmup_iterations):
            with Timer("warmup"):
                _ = self.model(dummy_input, verbose=False, device=self.device)

        self.logger.info("Model warmup completed")

    def detect(self, frame: np.ndarray) -> List[Detection]:
        """
        Detect persons in a frame

        Args:
            frame: Input frame (BGR format)

        Returns:
            List of Detection objects
        """
        detections = []

        try:
            with Timer("detection") as timer:
                # Run YOLO inference
                results = self.model(
                    frame,
                    conf=self.config.confidence_threshold,
                    iou=self.config.nms_threshold,
                    device=self.device,
                    verbose=False,
                    classes=[self.config.person_class_id]  # Only detect persons
                )

            # Update performance metrics
            self.total_inference_time += timer.get_elapsed_ms()

            # Process results
            for result in results:
                if result.boxes is not None:
                    boxes = result.boxes

                    for box in boxes:
                        # Extract box coordinates and confidence
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        confidence = box.conf[0].cpu().numpy()
                        class_id = int(box.cls[0].cpu().numpy())

                        # Create detection object
                        detection = Detection(
                            bbox=[float(x1), float(y1), float(x2), float(y2)],
                            confidence=float(confidence),
                            class_id=class_id
                        )
                        detections.append(detection)

            self.total_detections += len(detections)

            self.logger.debug(f"Detected {len(detections)} persons in {timer.get_elapsed_ms():.1f}ms")

        except Exception as e:
            self.logger.error(f"Error during detection: {e}")

        return detections

    def detect_batch(self, frames: List[np.ndarray]) -> List[List[Detection]]:
        """
        Detect persons in a batch of frames for improved GPU utilization

        Args:
            frames: List of input frames

        Returns:
            List of detection lists, one for each frame
        """
        all_detections = []

        try:
            with Timer("batch_detection") as timer:
                # Run batch inference
                results = self.model(
                    frames,
                    conf=self.config.confidence_threshold,
                    iou=self.config.nms_threshold,
                    device=self.device,
                    verbose=False,
                    classes=[self.config.person_class_id]
                )

            # Process results for each frame
            for result in results:
                frame_detections = []

                if result.boxes is not None:
                    boxes = result.boxes

                    for box in boxes:
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        confidence = box.conf[0].cpu().numpy()
                        class_id = int(box.cls[0].cpu().numpy())

                        detection = Detection(
                            bbox=[float(x1), float(y1), float(x2), float(y2)],
                            confidence=float(confidence),
                            class_id=class_id
                        )
                        frame_detections.append(detection)

                all_detections.append(frame_detections)
                self.total_detections += len(frame_detections)

            self.logger.debug(f"Batch detected {sum(len(d) for d in all_detections)} persons in {timer.get_elapsed_ms():.1f}ms")

        except Exception as e:
            self.logger.error(f"Error during batch detection: {e}")
            all_detections = [[] for _ in frames]

        return all_detections

    def visualize_detections(self, frame: np.ndarray, detections: List[Detection]) -> np.ndarray:
        """
        Draw detection bounding boxes on frame

        Args:
            frame: Input frame
            detections: List of detections

        Returns:
            Annotated frame
        """
        annotated_frame = frame.copy()

        for detection in detections:
            x1, y1, x2, y2 = map(int, detection.bbox)
            confidence = detection.confidence

            # Draw bounding box
            cv2.rectangle(annotated_frame, (x1, y1), (x2, y2), (0, 255, 0), 2)

            # Draw label
            label = f"Person: {confidence:.2f}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]

            # Draw label background
            cv2.rectangle(
                annotated_frame,
                (x1, y1 - label_size[1] - 10),
                (x1 + label_size[0], y1),
                (0, 255, 0),
                -1
            )

            # Draw label text
            cv2.putText(
                annotated_frame,
                label,
                (x1, y1 - 5),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.5,
                (0, 0, 0),
                2
            )

        return annotated_frame

    def get_performance_stats(self) -> dict:
        """Get performance statistics"""
        avg_inference_time = (
            self.total_inference_time / max(1, self.total_detections)
            if self.total_detections > 0 else 0
        )

        return {
            "total_detections": self.total_detections,
            "total_inference_time_ms": self.total_inference_time,
            "average_inference_time_ms": avg_inference_time,
            "device": self.device,
            "model_path": self.config.model_path
        }

    def cleanup(self):
        """Cleanup resources"""
        if self.device == "cuda":
            torch.cuda.empty_cache()
        self.logger.info("Detector cleanup completed")
