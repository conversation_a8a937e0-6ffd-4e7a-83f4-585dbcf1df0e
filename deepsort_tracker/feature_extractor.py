"""
Feature extraction for person re-identification in DeepSORT
"""
import cv2
import numpy as np
import torch
import onnxruntime as ort
import logging
from typing import List, Optional
from .config import FeatureConfig
from .detector import Detection
from .performance_monitor import Timer


logger = logging.getLogger(__name__)


class FeatureExtractor:
    """Extract appearance features for person re-identification"""
    
    def __init__(self, config: FeatureConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Initialize ONNX session
        self.session = None
        self.input_name = None
        self.output_name = None
        self._initialize_model()
        
        # Performance tracking
        self.total_extractions = 0
        self.total_extraction_time = 0.0
    
    def _initialize_model(self):
        """Initialize ONNX model for feature extraction"""
        try:
            self.logger.info(f"Loading feature extraction model: {self.config.model_path}")
            
            # Set up ONNX Runtime providers
            providers = []
            if self.config.device == "cuda" and torch.cuda.is_available():
                providers.append('CUDAExecutionProvider')
                self.logger.info("Using GPU for feature extraction")
            providers.append('CPUExecutionProvider')
            
            # Create ONNX Runtime session
            self.session = ort.InferenceSession(
                self.config.model_path,
                providers=providers
            )
            
            # Get input/output names
            self.input_name = self.session.get_inputs()[0].name
            self.output_name = self.session.get_outputs()[0].name
            
            # Get input shape
            input_shape = self.session.get_inputs()[0].shape
            self.logger.info(f"Model input shape: {input_shape}")
            
            self.logger.info("Feature extraction model initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize feature extraction model: {e}")
            raise
    
    def _preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """
        Preprocess image for feature extraction
        
        Args:
            image: Input image (BGR format)
            
        Returns:
            Preprocessed image tensor
        """
        # Resize to model input size
        resized = cv2.resize(image, self.config.input_size)
        
        # Convert BGR to RGB
        rgb = cv2.cvtColor(resized, cv2.COLOR_BGR2RGB)
        
        # Normalize to [0, 1]
        normalized = rgb.astype(np.float32) / 255.0
        
        # Normalize with ImageNet stats (common for person re-id models)
        mean = np.array([0.485, 0.456, 0.406])
        std = np.array([0.229, 0.224, 0.225])
        normalized = (normalized - mean) / std
        
        # Convert to CHW format and add batch dimension
        tensor = normalized.transpose(2, 0, 1)  # HWC to CHW
        tensor = np.expand_dims(tensor, axis=0)  # Add batch dimension
        
        return tensor
    
    def extract_features(self, frame: np.ndarray, detections: List[Detection]) -> List[Detection]:
        """
        Extract features for all detections in a frame
        
        Args:
            frame: Input frame
            detections: List of detections
            
        Returns:
            List of detections with features added
        """
        if not detections:
            return detections
        
        try:
            with Timer("feature_extraction") as timer:
                # Extract crops for all detections
                crops = []
                valid_detections = []
                
                for detection in detections:
                    crop = self._extract_crop(frame, detection)
                    if crop is not None:
                        crops.append(crop)
                        valid_detections.append(detection)
                
                if not crops:
                    return detections
                
                # Process in batches for efficiency
                features = self._extract_features_batch(crops)
                
                # Assign features to detections
                for detection, feature in zip(valid_detections, features):
                    detection.feature = feature
            
            self.total_extraction_time += timer.get_elapsed_ms()
            self.total_extractions += len(valid_detections)
            
            self.logger.debug(f"Extracted features for {len(valid_detections)} detections in {timer.get_elapsed_ms():.1f}ms")
            
        except Exception as e:
            self.logger.error(f"Error during feature extraction: {e}")
        
        return detections
    
    def _extract_crop(self, frame: np.ndarray, detection: Detection) -> Optional[np.ndarray]:
        """Extract and validate crop from detection"""
        try:
            x1, y1, x2, y2 = map(int, detection.bbox)
            
            # Validate coordinates
            h, w = frame.shape[:2]
            x1 = max(0, min(x1, w - 1))
            y1 = max(0, min(y1, h - 1))
            x2 = max(x1 + 1, min(x2, w))
            y2 = max(y1 + 1, min(y2, h))
            
            # Extract crop
            crop = frame[y1:y2, x1:x2]
            
            # Validate crop size
            if crop.shape[0] < 10 or crop.shape[1] < 10:
                return None
            
            return crop
            
        except Exception as e:
            self.logger.warning(f"Failed to extract crop: {e}")
            return None
    
    def _extract_features_batch(self, crops: List[np.ndarray]) -> List[np.ndarray]:
        """Extract features for a batch of crops"""
        features = []
        
        # Process in batches
        batch_size = self.config.batch_size
        for i in range(0, len(crops), batch_size):
            batch_crops = crops[i:i + batch_size]
            batch_features = self._process_batch(batch_crops)
            features.extend(batch_features)
        
        return features
    
    def _process_batch(self, crops: List[np.ndarray]) -> List[np.ndarray]:
        """Process a batch of crops through the model"""
        try:
            # Preprocess all crops
            batch_tensors = []
            for crop in crops:
                tensor = self._preprocess_image(crop)
                batch_tensors.append(tensor)
            
            # Stack into batch
            batch_input = np.vstack(batch_tensors)
            
            # Run inference
            outputs = self.session.run(
                [self.output_name],
                {self.input_name: batch_input}
            )
            
            # Extract features
            batch_features = outputs[0]
            
            # Normalize features (L2 normalization)
            normalized_features = []
            for feature in batch_features:
                norm = np.linalg.norm(feature)
                if norm > 0:
                    feature = feature / norm
                normalized_features.append(feature)
            
            return normalized_features
            
        except Exception as e:
            self.logger.error(f"Error processing batch: {e}")
            # Return zero features as fallback
            return [np.zeros(512, dtype=np.float32) for _ in crops]
    
    def compute_distance(self, feature1: np.ndarray, feature2: np.ndarray) -> float:
        """
        Compute cosine distance between two features
        
        Args:
            feature1: First feature vector
            feature2: Second feature vector
            
        Returns:
            Cosine distance (0 = identical, 2 = opposite)
        """
        try:
            # Compute cosine similarity
            dot_product = np.dot(feature1, feature2)
            
            # Clamp to avoid numerical issues
            dot_product = np.clip(dot_product, -1.0, 1.0)
            
            # Convert to distance (0 = identical, 2 = opposite)
            distance = 1.0 - dot_product
            
            return float(distance)
            
        except Exception as e:
            self.logger.warning(f"Error computing distance: {e}")
            return 1.0  # Return neutral distance
    
    def get_performance_stats(self) -> dict:
        """Get performance statistics"""
        avg_extraction_time = (
            self.total_extraction_time / max(1, self.total_extractions)
            if self.total_extractions > 0 else 0
        )
        
        return {
            "total_extractions": self.total_extractions,
            "total_extraction_time_ms": self.total_extraction_time,
            "average_extraction_time_ms": avg_extraction_time,
            "model_path": self.config.model_path,
            "input_size": self.config.input_size,
            "batch_size": self.config.batch_size
        }
    
    def cleanup(self):
        """Cleanup resources"""
        if self.session:
            del self.session
        self.logger.info("Feature extractor cleanup completed")
