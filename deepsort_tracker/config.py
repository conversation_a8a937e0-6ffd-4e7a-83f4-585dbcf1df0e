"""
Configuration settings for the DeepSORT tracking system
"""
import os
from dataclasses import dataclass
from typing import <PERSON>ple, Optional


@dataclass
class DetectionConfig:
    """YOLO detection configuration"""
    model_path: str = "yolov8n.pt"  # Can be yolov8n.pt, yolov8s.pt, yolov8m.pt, yolov8l.pt, yolov8x.pt
    confidence_threshold: float = 0.5
    nms_threshold: float = 0.4
    device: str = "cuda"  # "cuda" or "cpu"
    input_size: Tuple[int, int] = (640, 640)
    person_class_id: int = 0  # COCO person class


@dataclass
class TrackingConfig:
    """DeepSORT tracking configuration"""
    max_age: int = 70  # Maximum number of frames to keep alive a track without associated detections
    n_init: int = 3  # Number of consecutive detections before the track is confirmed
    max_iou_distance: float = 0.7  # Maximum IoU distance for matching
    max_cosine_distance: float = 0.2  # Maximum cosine distance for appearance matching
    nn_budget: int = 100  # Maximum size of the appearance descriptor gallery


@dataclass
class FeatureConfig:
    """Feature extraction configuration"""
    model_path: str = "weights/resnet50_market1501_aicity156.onnx"
    device: str = "cuda"
    input_size: Tuple[int, int] = (128, 256)  # Width, Height for person re-id
    batch_size: int = 32


@dataclass
class ProcessingConfig:
    """Processing and performance configuration"""
    num_detection_workers: int = 2
    num_tracking_workers: int = 1
    queue_size: int = 100
    detection_interval: int = 1  # Process every N frames for detection
    tracking_interval: int = 1  # Process every N frames for tracking
    enable_gpu: bool = True
    enable_multiprocessing: bool = True


@dataclass
class VideoConfig:
    """Video processing configuration"""
    input_path: str = ""
    output_path: str = "output/tracked_video.mp4"
    output_fps: int = 30
    output_codec: str = "mp4v"
    save_frames: bool = False
    frame_save_dir: str = "output/frames"
    display_video: bool = True
    max_frames: Optional[int] = None  # Limit processing to N frames


@dataclass
class LoggingConfig:
    """Logging configuration"""
    level: str = "INFO"  # DEBUG, INFO, WARNING, ERROR
    log_file: str = "output/tracking.log"
    enable_performance_logging: bool = True
    log_interval: int = 100  # Log performance every N frames


@dataclass
class Config:
    """Main configuration class"""
    detection: DetectionConfig = None
    tracking: TrackingConfig = None
    feature: FeatureConfig = None
    processing: ProcessingConfig = None
    video: VideoConfig = None
    logging: LoggingConfig = None

    def __post_init__(self):
        """Initialize default values after dataclass creation"""
        if self.detection is None:
            self.detection = DetectionConfig()
        if self.tracking is None:
            self.tracking = TrackingConfig()
        if self.feature is None:
            self.feature = FeatureConfig()
        if self.processing is None:
            self.processing = ProcessingConfig()
        if self.video is None:
            self.video = VideoConfig()
        if self.logging is None:
            self.logging = LoggingConfig()

    @classmethod
    def from_env(cls) -> 'Config':
        """Create configuration from environment variables"""
        config = cls()

        # Detection config from env
        config.detection.model_path = os.getenv('YOLO_MODEL_PATH', config.detection.model_path)
        config.detection.confidence_threshold = float(os.getenv('CONFIDENCE_THRESHOLD', config.detection.confidence_threshold))
        config.detection.device = os.getenv('DETECTION_DEVICE', config.detection.device)

        # Tracking config from env
        config.tracking.max_age = int(os.getenv('MAX_AGE', config.tracking.max_age))
        config.tracking.n_init = int(os.getenv('N_INIT', config.tracking.n_init))
        config.tracking.max_iou_distance = float(os.getenv('MAX_IOU_DISTANCE', config.tracking.max_iou_distance))

        # Feature config from env
        config.feature.model_path = os.getenv('FEATURE_MODEL_PATH', config.feature.model_path)
        config.feature.device = os.getenv('FEATURE_DEVICE', config.feature.device)

        # Processing config from env
        config.processing.num_detection_workers = int(os.getenv('NUM_DETECTION_WORKERS', config.processing.num_detection_workers))
        config.processing.enable_gpu = os.getenv('ENABLE_GPU', 'true').lower() == 'true'
        config.processing.enable_multiprocessing = os.getenv('ENABLE_MULTIPROCESSING', 'true').lower() == 'true'

        # Video config from env
        config.video.input_path = os.getenv('INPUT_VIDEO', config.video.input_path)
        config.video.output_path = os.getenv('OUTPUT_VIDEO', config.video.output_path)
        config.video.display_video = os.getenv('DISPLAY_VIDEO', 'true').lower() == 'true'

        return config

    def validate(self) -> bool:
        """Validate configuration settings"""
        errors = []

        # Check if input video exists
        if self.video.input_path and not os.path.exists(self.video.input_path):
            errors.append(f"Input video not found: {self.video.input_path}")

        # Check if YOLO model exists
        if not os.path.exists(self.detection.model_path):
            errors.append(f"YOLO model not found: {self.detection.model_path}")

        # Check if feature model exists
        if not os.path.exists(self.feature.model_path):
            errors.append(f"Feature model not found: {self.feature.model_path}")

        # Validate thresholds
        if not 0 <= self.detection.confidence_threshold <= 1:
            errors.append("Confidence threshold must be between 0 and 1")

        if not 0 <= self.tracking.max_iou_distance <= 1:
            errors.append("Max IoU distance must be between 0 and 1")

        if errors:
            for error in errors:
                print(f"Configuration error: {error}")
            return False

        return True

    def create_output_dirs(self):
        """Create necessary output directories"""
        os.makedirs(os.path.dirname(self.video.output_path), exist_ok=True)
        os.makedirs(os.path.dirname(self.logging.log_file), exist_ok=True)

        if self.video.save_frames:
            os.makedirs(self.video.frame_save_dir, exist_ok=True)


# Default configuration instance
default_config = Config()
