"""
Example usage of the DeepSORT tracking system
"""
import os
import sys
from .config import Config
from .main import Deep<PERSON><PERSON><PERSON><PERSON>
from .utils import setup_logging


def example_video_tracking():
    """Example: Track persons in a video file"""
    print("Example: Video Tracking")
    print("-" * 30)
    
    # Create configuration
    config = Config()
    
    # Configure for video tracking
    config.video.input_path = "video/sample_video.mp4"  # Update with your video path
    config.video.output_path = "output/tracked_video.mp4"
    config.video.display_video = True
    config.video.save_frames = False
    
    # Detection settings
    config.detection.model_path = "yolov8n.pt"  # Will download automatically
    config.detection.confidence_threshold = 0.5
    config.detection.device = "cuda"  # Use "cpu" if no GPU
    
    # Tracking settings
    config.tracking.max_age = 70
    config.tracking.n_init = 3
    config.tracking.max_iou_distance = 0.7
    
    # Performance settings
    config.processing.enable_multiprocessing = True
    config.processing.num_detection_workers = 2
    
    # Logging
    config.logging.level = "INFO"
    config.logging.enable_performance_logging = True
    
    # Setup logging
    setup_logging(config)
    
    # Create and run application
    try:
        app = DeepSORTApp(config)
        app.initialize()
        app.run()
        print("✓ Video tracking completed successfully!")
    except Exception as e:
        print(f"✗ Error: {e}")


def example_webcam_tracking():
    """Example: Real-time tracking with webcam"""
    print("Example: Webcam Tracking")
    print("-" * 30)
    
    # Create configuration
    config = Config()
    
    # Configure for webcam tracking
    config.video.input_path = ""  # Empty for webcam
    config.video.output_path = "output/webcam_tracking.mp4"
    config.video.display_video = True
    
    # Use faster settings for real-time
    config.detection.model_path = "yolov8n.pt"  # Fastest model
    config.detection.confidence_threshold = 0.6
    config.detection.device = "cuda"
    
    # Tracking settings
    config.tracking.max_age = 30  # Shorter for real-time
    config.tracking.n_init = 2
    
    # Performance settings
    config.processing.enable_multiprocessing = True
    config.processing.num_detection_workers = 1  # Less workers for real-time
    
    # Logging
    config.logging.level = "INFO"
    
    # Setup logging
    setup_logging(config)
    
    # Create and run application
    try:
        app = DeepSORTApp(config)
        app.initialize()
        print("Press 'q' to quit webcam tracking")
        app.run()
        print("✓ Webcam tracking completed!")
    except Exception as e:
        print(f"✗ Error: {e}")


def example_high_accuracy_tracking():
    """Example: High accuracy tracking with larger models"""
    print("Example: High Accuracy Tracking")
    print("-" * 35)
    
    # Create configuration
    config = Config()
    
    # Configure for high accuracy
    config.video.input_path = "video/sample_video.mp4"
    config.video.output_path = "output/high_accuracy_tracking.mp4"
    config.video.display_video = True
    
    # Use larger, more accurate models
    config.detection.model_path = "yolov8l.pt"  # Larger model
    config.detection.confidence_threshold = 0.3  # Lower threshold
    config.detection.device = "cuda"
    
    # Feature extraction settings
    config.feature.model_path = "weights/resnet50_market1501_aicity156.onnx"
    config.feature.device = "cuda"
    config.feature.batch_size = 16  # Larger batch for efficiency
    
    # Tracking settings for better accuracy
    config.tracking.max_age = 100  # Keep tracks longer
    config.tracking.n_init = 5  # More confirmations needed
    config.tracking.max_iou_distance = 0.8
    config.tracking.max_cosine_distance = 0.15  # Stricter appearance matching
    
    # Performance settings
    config.processing.enable_multiprocessing = True
    config.processing.num_detection_workers = 3
    
    # Logging
    config.logging.level = "INFO"
    config.logging.enable_performance_logging = True
    
    # Setup logging
    setup_logging(config)
    
    # Create and run application
    try:
        app = DeepSORTApp(config)
        app.initialize()
        app.run()
        print("✓ High accuracy tracking completed!")
    except Exception as e:
        print(f"✗ Error: {e}")


def example_performance_optimized():
    """Example: Performance-optimized tracking"""
    print("Example: Performance Optimized Tracking")
    print("-" * 40)
    
    # Create configuration
    config = Config()
    
    # Configure for maximum performance
    config.video.input_path = "video/sample_video.mp4"
    config.video.output_path = "output/fast_tracking.mp4"
    config.video.display_video = False  # Disable display for speed
    
    # Fast detection settings
    config.detection.model_path = "yolov8n.pt"  # Fastest model
    config.detection.confidence_threshold = 0.7  # Higher threshold
    config.detection.device = "cuda"
    
    # Simplified tracking
    config.tracking.max_age = 20  # Shorter track life
    config.tracking.n_init = 1  # Immediate confirmation
    config.tracking.max_iou_distance = 0.9  # More permissive IoU
    
    # Maximum multiprocessing
    config.processing.enable_multiprocessing = True
    config.processing.num_detection_workers = 4  # More workers
    config.processing.queue_size = 200  # Larger queues
    
    # Minimal logging
    config.logging.level = "WARNING"
    config.logging.enable_performance_logging = True
    
    # Setup logging
    setup_logging(config)
    
    # Create and run application
    try:
        app = DeepSORTApp(config)
        app.initialize()
        app.run()
        print("✓ Performance optimized tracking completed!")
    except Exception as e:
        print(f"✗ Error: {e}")


def main():
    """Main example selector"""
    print("DeepSORT Tracking System Examples")
    print("=" * 40)
    print("1. Video Tracking (Balanced)")
    print("2. Webcam Tracking (Real-time)")
    print("3. High Accuracy Tracking")
    print("4. Performance Optimized")
    print("5. Run All Examples")
    print("0. Exit")
    
    while True:
        try:
            choice = input("\nSelect an example (0-5): ").strip()
            
            if choice == "0":
                print("Goodbye!")
                break
            elif choice == "1":
                example_video_tracking()
            elif choice == "2":
                example_webcam_tracking()
            elif choice == "3":
                example_high_accuracy_tracking()
            elif choice == "4":
                example_performance_optimized()
            elif choice == "5":
                print("Running all examples...")
                example_video_tracking()
                example_webcam_tracking()
                example_high_accuracy_tracking()
                example_performance_optimized()
                print("All examples completed!")
            else:
                print("Invalid choice. Please select 0-5.")
                
        except KeyboardInterrupt:
            print("\nInterrupted by user. Goodbye!")
            break
        except Exception as e:
            print(f"Error: {e}")


if __name__ == "__main__":
    main()
