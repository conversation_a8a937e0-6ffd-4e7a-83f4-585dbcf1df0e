"""
DeepSORT Tracking System with GPU optimization and multiprocessing

A comprehensive implementation of DeepSORT for multi-object tracking with:
- GPU-optimized YOLO detection
- Appearance-based feature extraction
- Kalman filtering for motion prediction
- Multiprocessing for improved performance
- Comprehensive performance monitoring

Author: AI Assistant
Version: 1.0.0
"""

from .config import Config, default_config
from .detector import Y<PERSON>ODetector, Detection
from .feature_extractor import FeatureExtractor
from .deep_sort import DeepSORTTracker, Track, TrackState
from .multiprocess_tracker import MultiProcessTracker
from .performance_monitor import PerformanceMonitor, Timer
from .utils import setup_logging, draw_tracks, draw_performance_info

__version__ = "1.0.0"
__author__ = "AI Assistant"

__all__ = [
    # Configuration
    'Config',
    'default_config',
    
    # Core components
    'YOLODetector',
    'Detection',
    'FeatureExtractor',
    'DeepSORTTracker',
    'Track',
    'TrackState',
    
    # Multiprocessing
    'MultiProcessTracker',
    
    # Performance monitoring
    'PerformanceMonitor',
    'Timer',
    
    # Utilities
    'setup_logging',
    'draw_tracks',
    'draw_performance_info',
]
