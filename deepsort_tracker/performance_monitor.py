"""
Performance monitoring and timing utilities for the DeepSORT tracking system
"""
import time
import logging
import threading
from collections import defaultdict, deque
from dataclasses import dataclass, field
from typing import Dict, List, Optional
import numpy as np


@dataclass
class PerformanceMetrics:
    """Container for performance metrics"""
    total_frames: int = 0
    detection_time: float = 0.0
    tracking_time: float = 0.0
    feature_extraction_time: float = 0.0
    total_processing_time: float = 0.0
    fps: float = 0.0
    detection_fps: float = 0.0
    tracking_fps: float = 0.0
    memory_usage_mb: float = 0.0
    gpu_memory_usage_mb: float = 0.0
    active_tracks: int = 0
    total_detections: int = 0


class Timer:
    """Context manager for timing operations"""
    
    def __init__(self, name: str = "operation"):
        self.name = name
        self.start_time = None
        self.end_time = None
        self.elapsed_time = None
    
    def __enter__(self):
        self.start_time = time.perf_counter()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end_time = time.perf_counter()
        self.elapsed_time = self.end_time - self.start_time
    
    def get_elapsed_ms(self) -> float:
        """Get elapsed time in milliseconds"""
        return self.elapsed_time * 1000 if self.elapsed_time else 0.0


class PerformanceMonitor:
    """Monitor and track performance metrics for the tracking system"""
    
    def __init__(self, window_size: int = 100, log_interval: int = 100):
        self.window_size = window_size
        self.log_interval = log_interval
        self.logger = logging.getLogger(__name__)
        
        # Thread-safe metrics storage
        self._lock = threading.Lock()
        
        # Timing data with sliding windows
        self.detection_times = deque(maxlen=window_size)
        self.tracking_times = deque(maxlen=window_size)
        self.feature_times = deque(maxlen=window_size)
        self.total_times = deque(maxlen=window_size)
        
        # Frame counters
        self.frame_count = 0
        self.detection_count = 0
        self.tracking_count = 0
        
        # System metrics
        self.start_time = time.time()
        self.last_log_time = time.time()
        
        # Track statistics
        self.track_stats = defaultdict(int)
        self.detection_stats = defaultdict(int)
        
        # Memory tracking
        self._memory_usage = deque(maxlen=window_size)
        self._gpu_memory_usage = deque(maxlen=window_size)
    
    def record_detection_time(self, elapsed_time: float, num_detections: int = 0):
        """Record detection timing"""
        with self._lock:
            self.detection_times.append(elapsed_time)
            self.detection_count += 1
            self.detection_stats['total_detections'] += num_detections
    
    def record_tracking_time(self, elapsed_time: float, num_tracks: int = 0):
        """Record tracking timing"""
        with self._lock:
            self.tracking_times.append(elapsed_time)
            self.tracking_count += 1
            self.track_stats['active_tracks'] = num_tracks
    
    def record_feature_time(self, elapsed_time: float):
        """Record feature extraction timing"""
        with self._lock:
            self.feature_times.append(elapsed_time)
    
    def record_total_time(self, elapsed_time: float):
        """Record total frame processing time"""
        with self._lock:
            self.total_times.append(elapsed_time)
            self.frame_count += 1
    
    def record_memory_usage(self, memory_mb: float, gpu_memory_mb: float = 0.0):
        """Record memory usage"""
        with self._lock:
            self._memory_usage.append(memory_mb)
            self._gpu_memory_usage.append(gpu_memory_mb)
    
    def get_current_metrics(self) -> PerformanceMetrics:
        """Get current performance metrics"""
        with self._lock:
            current_time = time.time()
            elapsed_total = current_time - self.start_time
            
            metrics = PerformanceMetrics()
            metrics.total_frames = self.frame_count
            
            # Calculate average times
            if self.detection_times:
                metrics.detection_time = np.mean(self.detection_times)
                metrics.detection_fps = 1.0 / metrics.detection_time if metrics.detection_time > 0 else 0.0
            
            if self.tracking_times:
                metrics.tracking_time = np.mean(self.tracking_times)
                metrics.tracking_fps = 1.0 / metrics.tracking_time if metrics.tracking_time > 0 else 0.0
            
            if self.feature_times:
                metrics.feature_extraction_time = np.mean(self.feature_times)
            
            if self.total_times:
                metrics.total_processing_time = np.mean(self.total_times)
                metrics.fps = 1.0 / metrics.total_processing_time if metrics.total_processing_time > 0 else 0.0
            
            # Overall FPS
            if elapsed_total > 0:
                metrics.fps = self.frame_count / elapsed_total
            
            # Memory usage
            if self._memory_usage:
                metrics.memory_usage_mb = np.mean(self._memory_usage)
            
            if self._gpu_memory_usage:
                metrics.gpu_memory_usage_mb = np.mean(self._gpu_memory_usage)
            
            # Track statistics
            metrics.active_tracks = self.track_stats.get('active_tracks', 0)
            metrics.total_detections = self.detection_stats.get('total_detections', 0)
            
            return metrics
    
    def log_performance(self, force: bool = False):
        """Log current performance metrics"""
        current_time = time.time()
        
        if not force and (current_time - self.last_log_time) < self.log_interval:
            return
        
        metrics = self.get_current_metrics()
        
        self.logger.info(
            f"Performance Metrics - "
            f"Frames: {metrics.total_frames}, "
            f"FPS: {metrics.fps:.2f}, "
            f"Detection: {metrics.detection_time*1000:.1f}ms ({metrics.detection_fps:.1f} FPS), "
            f"Tracking: {metrics.tracking_time*1000:.1f}ms ({metrics.tracking_fps:.1f} FPS), "
            f"Feature: {metrics.feature_extraction_time*1000:.1f}ms, "
            f"Total: {metrics.total_processing_time*1000:.1f}ms, "
            f"Tracks: {metrics.active_tracks}, "
            f"Detections: {metrics.total_detections}, "
            f"Memory: {metrics.memory_usage_mb:.1f}MB"
        )
        
        if metrics.gpu_memory_usage_mb > 0:
            self.logger.info(f"GPU Memory: {metrics.gpu_memory_usage_mb:.1f}MB")
        
        self.last_log_time = current_time
    
    def get_summary_report(self) -> str:
        """Generate a summary performance report"""
        metrics = self.get_current_metrics()
        elapsed_total = time.time() - self.start_time
        
        report = f"""
=== DeepSORT Tracking Performance Summary ===
Total Runtime: {elapsed_total:.2f} seconds
Total Frames Processed: {metrics.total_frames}
Average FPS: {metrics.fps:.2f}

Detection Performance:
  - Average Time: {metrics.detection_time*1000:.2f}ms
  - Average FPS: {metrics.detection_fps:.2f}
  - Total Detections: {metrics.total_detections}

Tracking Performance:
  - Average Time: {metrics.tracking_time*1000:.2f}ms
  - Average FPS: {metrics.tracking_fps:.2f}
  - Active Tracks: {metrics.active_tracks}

Feature Extraction:
  - Average Time: {metrics.feature_extraction_time*1000:.2f}ms

Memory Usage:
  - Average RAM: {metrics.memory_usage_mb:.1f}MB
  - Average GPU: {metrics.gpu_memory_usage_mb:.1f}MB

Overall Processing:
  - Average Frame Time: {metrics.total_processing_time*1000:.2f}ms
  - Efficiency: {(metrics.fps / 30.0 * 100):.1f}% (assuming 30 FPS target)
"""
        return report
    
    def reset(self):
        """Reset all metrics"""
        with self._lock:
            self.detection_times.clear()
            self.tracking_times.clear()
            self.feature_times.clear()
            self.total_times.clear()
            
            self.frame_count = 0
            self.detection_count = 0
            self.tracking_count = 0
            
            self.start_time = time.time()
            self.last_log_time = time.time()
            
            self.track_stats.clear()
            self.detection_stats.clear()
            
            self._memory_usage.clear()
            self._gpu_memory_usage.clear()


def get_memory_usage() -> tuple:
    """Get current memory usage in MB"""
    try:
        import psutil
        process = psutil.Process()
        memory_info = process.memory_info()
        return memory_info.rss / 1024 / 1024, 0.0  # RAM in MB, GPU placeholder
    except ImportError:
        return 0.0, 0.0


def get_gpu_memory_usage() -> float:
    """Get current GPU memory usage in MB"""
    try:
        import torch
        if torch.cuda.is_available():
            return torch.cuda.memory_allocated() / 1024 / 1024
    except ImportError:
        pass
    return 0.0
