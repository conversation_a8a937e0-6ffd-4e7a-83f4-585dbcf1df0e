"""
Test script for the DeepSORT tracking system
"""
import cv2
import numpy as np
import logging
import time
from .config import Config
from .detector import YOLODetector
from .feature_extractor import FeatureExtractor
from .deep_sort.tracker import DeepSORTTracker
from .deep_sort.nn_matching import NearestNeighborDistanceMetric
from .deep_sort.detection import Detection as DeepSortDetection
from .utils import setup_logging, draw_tracks


def create_test_config():
    """Create a test configuration"""
    config = Config()
    
    # Use smaller models for testing
    config.detection.model_path = "yolov8n.pt"
    config.detection.confidence_threshold = 0.5
    config.detection.device = "cuda" if cv2.cuda.getCudaEnabledDeviceCount() > 0 else "cpu"
    
    # Feature extraction (use dummy path for testing)
    config.feature.model_path = "weights/resnet50_market1501_aicity156.onnx"
    config.feature.device = config.detection.device
    
    # Tracking settings
    config.tracking.max_age = 30
    config.tracking.n_init = 3
    config.tracking.max_iou_distance = 0.7
    config.tracking.max_cosine_distance = 0.2
    
    # Disable multiprocessing for testing
    config.processing.enable_multiprocessing = False
    
    # Video settings
    config.video.display_video = True
    config.video.output_path = "output/test_tracking.mp4"
    
    # Logging
    config.logging.level = "INFO"
    config.logging.log_file = "output/test.log"
    
    return config


def test_detector():
    """Test YOLO detector"""
    print("Testing YOLO detector...")
    
    config = create_test_config()
    setup_logging(config)
    
    try:
        detector = YOLODetector(config.detection)
        
        # Create test image
        test_image = np.random.randint(0, 255, (640, 480, 3), dtype=np.uint8)
        
        # Run detection
        start_time = time.time()
        detections = detector.detect(test_image)
        detection_time = time.time() - start_time
        
        print(f"Detection completed in {detection_time*1000:.1f}ms")
        print(f"Found {len(detections)} detections")
        
        # Test batch detection
        batch_images = [test_image] * 4
        start_time = time.time()
        batch_detections = detector.batch_detect(batch_images)
        batch_time = time.time() - start_time
        
        print(f"Batch detection (4 images) completed in {batch_time*1000:.1f}ms")
        print(f"Average per image: {batch_time/4*1000:.1f}ms")
        
        detector.cleanup()
        print("✓ Detector test passed")
        return True
        
    except Exception as e:
        print(f"✗ Detector test failed: {e}")
        return False


def test_feature_extractor():
    """Test feature extractor"""
    print("Testing feature extractor...")
    
    config = create_test_config()
    
    # Check if feature model exists
    import os
    if not os.path.exists(config.feature.model_path):
        print(f"⚠ Feature model not found: {config.feature.model_path}")
        print("Skipping feature extractor test")
        return True
    
    try:
        feature_extractor = FeatureExtractor(config.feature)
        
        # Create test image and detections
        test_image = np.random.randint(0, 255, (640, 480, 3), dtype=np.uint8)
        
        # Create dummy detections
        from .detector import Detection
        detections = [
            Detection([100, 100, 200, 300], 0.8),  # Person-like bbox
            Detection([300, 150, 400, 350], 0.9),
        ]
        
        # Extract features
        start_time = time.time()
        detections_with_features = feature_extractor.extract_features(test_image, detections)
        extraction_time = time.time() - start_time
        
        print(f"Feature extraction completed in {extraction_time*1000:.1f}ms")
        print(f"Extracted features for {len(detections_with_features)} detections")
        
        # Check if features were added
        for i, detection in enumerate(detections_with_features):
            if detection.feature is not None:
                print(f"Detection {i}: feature shape {detection.feature.shape}")
            else:
                print(f"Detection {i}: no feature extracted")
        
        feature_extractor.cleanup()
        print("✓ Feature extractor test passed")
        return True
        
    except Exception as e:
        print(f"✗ Feature extractor test failed: {e}")
        return False


def test_tracker():
    """Test DeepSORT tracker"""
    print("Testing DeepSORT tracker...")
    
    try:
        # Initialize tracker
        metric = NearestNeighborDistanceMetric("cosine", 0.2, 100)
        tracker = DeepSORTTracker(metric, max_iou_distance=0.7, max_age=30, n_init=3)
        
        # Create test detections
        detections = []
        for i in range(3):
            # Create dummy detection with feature
            detection = DeepSortDetection(
                tlwh=np.array([100 + i*50, 100, 50, 100]),
                confidence=0.8,
                feature=np.random.randn(256).astype(np.float32)
            )
            detections.append(detection)
        
        # Update tracker
        start_time = time.time()
        tracks = tracker.update(detections)
        tracking_time = time.time() - start_time
        
        print(f"Tracking update completed in {tracking_time*1000:.1f}ms")
        print(f"Active tracks: {len(tracks)}")
        
        # Test multiple updates
        for frame in range(5):
            # Simulate moving detections
            moved_detections = []
            for i, detection in enumerate(detections):
                new_tlwh = detection.tlwh.copy()
                new_tlwh[0] += frame * 5  # Move right
                moved_detection = DeepSortDetection(
                    tlwh=new_tlwh,
                    confidence=0.8,
                    feature=np.random.randn(256).astype(np.float32)
                )
                moved_detections.append(moved_detection)
            
            tracks = tracker.update(moved_detections)
            print(f"Frame {frame+1}: {len(tracks)} tracks")
        
        print("✓ Tracker test passed")
        return True
        
    except Exception as e:
        print(f"✗ Tracker test failed: {e}")
        return False


def test_webcam():
    """Test with webcam input"""
    print("Testing with webcam...")
    
    config = create_test_config()
    setup_logging(config)
    
    # Check if webcam is available
    cap = cv2.VideoCapture(0)
    if not cap.isOpened():
        print("⚠ Webcam not available, skipping webcam test")
        return True
    
    try:
        # Initialize components
        detector = YOLODetector(config.detection)
        
        # Initialize tracker
        metric = NearestNeighborDistanceMetric("cosine", 0.2, 100)
        tracker = DeepSORTTracker(metric, max_iou_distance=0.7, max_age=30, n_init=3)
        
        print("Press 'q' to quit webcam test")
        frame_count = 0
        
        while frame_count < 100:  # Limit test duration
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            # Run detection
            detections = detector.detect(frame)
            
            # Convert to DeepSORT format (without features for simplicity)
            deepsort_detections = []
            for detection in detections:
                x1, y1, x2, y2 = detection.bbox
                tlwh = [x1, y1, x2 - x1, y2 - y1]
                deepsort_detection = DeepSortDetection(
                    tlwh=np.array(tlwh),
                    confidence=detection.confidence,
                    feature=np.random.randn(256).astype(np.float32)  # Dummy feature
                )
                deepsort_detections.append(deepsort_detection)
            
            # Update tracker
            tracker.predict()
            tracks = tracker.update(deepsort_detections)
            
            # Draw results
            annotated_frame = draw_tracks(frame, tracks)
            
            # Add frame info
            cv2.putText(annotated_frame, f"Frame: {frame_count}", (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(annotated_frame, f"Tracks: {len(tracks)}", (10, 70),
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            
            cv2.imshow('Webcam Test', annotated_frame)
            
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
        
        cap.release()
        cv2.destroyAllWindows()
        detector.cleanup()
        
        print("✓ Webcam test completed")
        return True
        
    except Exception as e:
        print(f"✗ Webcam test failed: {e}")
        cap.release()
        cv2.destroyAllWindows()
        return False


def run_all_tests():
    """Run all tests"""
    print("=" * 50)
    print("DeepSORT Tracking System Test Suite")
    print("=" * 50)
    
    tests = [
        ("Detector", test_detector),
        ("Feature Extractor", test_feature_extractor),
        ("Tracker", test_tracker),
        ("Webcam", test_webcam),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n--- {test_name} Test ---")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Print summary
    print("\n" + "=" * 50)
    print("Test Results Summary")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name:20} : {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        print("🎉 All tests passed!")
    else:
        print("⚠ Some tests failed. Check the output above for details.")
    
    return passed == len(results)


if __name__ == "__main__":
    run_all_tests()
