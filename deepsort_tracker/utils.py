"""
Utility functions for the DeepSORT tracking system
"""
import cv2
import numpy as np
import os
import logging
from typing import List, Tuple, Optional
from .deep_sort.track import Track
from .detector import Detection


def setup_logging(config):
    """Setup logging configuration"""
    # Create output directory if it doesn't exist
    log_dir = os.path.dirname(config.logging.log_file)
    if log_dir:
        os.makedirs(log_dir, exist_ok=True)
    
    # Configure logging
    logging.basicConfig(
        level=getattr(logging, config.logging.level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(config.logging.log_file),
            logging.StreamHandler()
        ]
    )


def draw_tracks(frame: np.ndarray, tracks: List[Track], detections: List[Detection] = None) -> np.ndarray:
    """
    Draw tracking results on frame
    
    Args:
        frame: Input frame
        tracks: List of tracks
        detections: Optional list of detections to draw
        
    Returns:
        Annotated frame
    """
    annotated_frame = frame.copy()
    
    # Draw detections if provided
    if detections:
        for detection in detections:
            x1, y1, x2, y2 = map(int, detection.bbox)
            cv2.rectangle(annotated_frame, (x1, y1), (x2, y2), (0, 255, 0), 1)
            
            # Draw confidence
            label = f"Det: {detection.confidence:.2f}"
            cv2.putText(annotated_frame, label, (x1, y1 - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)
    
    # Draw tracks
    for track in tracks:
        if not track.is_confirmed():
            continue
            
        bbox = track.to_tlbr()
        x1, y1, x2, y2 = map(int, bbox)
        
        # Choose color based on track ID
        color = get_track_color(track.track_id)
        
        # Draw bounding box
        cv2.rectangle(annotated_frame, (x1, y1), (x2, y2), color, 2)
        
        # Draw track ID
        label = f"ID: {track.track_id}"
        label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
        
        # Draw label background
        cv2.rectangle(
            annotated_frame,
            (x1, y1 - label_size[1] - 10),
            (x1 + label_size[0], y1),
            color,
            -1
        )
        
        # Draw label text
        cv2.putText(
            annotated_frame,
            label,
            (x1, y1 - 5),
            cv2.FONT_HERSHEY_SIMPLEX,
            0.6,
            (255, 255, 255),
            2
        )
        
        # Draw center point
        center = track.center.astype(int)
        cv2.circle(annotated_frame, tuple(center), 3, color, -1)
        
        # Draw velocity vector if available
        velocity = track.velocity
        if np.linalg.norm(velocity) > 0.1:  # Only draw if velocity is significant
            end_point = (center + velocity * 10).astype(int)
            cv2.arrowedLine(annotated_frame, tuple(center), tuple(end_point), color, 2)
    
    return annotated_frame


def get_track_color(track_id: int) -> Tuple[int, int, int]:
    """Get consistent color for track ID"""
    # Generate color based on track ID
    np.random.seed(track_id)
    color = tuple(map(int, np.random.randint(0, 255, 3)))
    return color


def draw_performance_info(frame: np.ndarray, fps: float, num_tracks: int, 
                         num_detections: int, frame_id: int) -> np.ndarray:
    """Draw performance information on frame"""
    annotated_frame = frame.copy()
    
    # Performance text
    info_text = [
        f"Frame: {frame_id}",
        f"FPS: {fps:.1f}",
        f"Tracks: {num_tracks}",
        f"Detections: {num_detections}"
    ]
    
    # Draw background
    text_height = 25
    bg_height = len(info_text) * text_height + 10
    cv2.rectangle(annotated_frame, (10, 10), (250, bg_height), (0, 0, 0), -1)
    cv2.rectangle(annotated_frame, (10, 10), (250, bg_height), (255, 255, 255), 2)
    
    # Draw text
    for i, text in enumerate(info_text):
        y_pos = 35 + i * text_height
        cv2.putText(annotated_frame, text, (20, y_pos), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    
    return annotated_frame


def save_frame(frame: np.ndarray, output_dir: str, frame_id: int):
    """Save frame to output directory"""
    os.makedirs(output_dir, exist_ok=True)
    filename = f"frame_{frame_id:06d}.jpg"
    filepath = os.path.join(output_dir, filename)
    cv2.imwrite(filepath, frame)


def create_video_writer(output_path: str, fps: int, frame_size: Tuple[int, int], 
                       codec: str = "mp4v") -> cv2.VideoWriter:
    """Create video writer for output"""
    # Create output directory
    output_dir = os.path.dirname(output_path)
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
    
    # Create video writer
    fourcc = cv2.VideoWriter_fourcc(*codec)
    writer = cv2.VideoWriter(output_path, fourcc, fps, frame_size)
    
    if not writer.isOpened():
        raise RuntimeError(f"Failed to create video writer for {output_path}")
    
    return writer


def validate_video_input(video_path: str) -> Tuple[bool, dict]:
    """Validate video input and get properties"""
    if not os.path.exists(video_path):
        return False, {"error": f"Video file not found: {video_path}"}
    
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        return False, {"error": f"Cannot open video file: {video_path}"}
    
    # Get video properties
    properties = {
        "frame_count": int(cap.get(cv2.CAP_PROP_FRAME_COUNT)),
        "fps": cap.get(cv2.CAP_PROP_FPS),
        "width": int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)),
        "height": int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT)),
        "duration": int(cap.get(cv2.CAP_PROP_FRAME_COUNT)) / cap.get(cv2.CAP_PROP_FPS)
    }
    
    cap.release()
    return True, properties


def resize_frame(frame: np.ndarray, target_size: Optional[Tuple[int, int]] = None, 
                max_size: Optional[int] = None) -> np.ndarray:
    """Resize frame while maintaining aspect ratio"""
    if target_size is None and max_size is None:
        return frame
    
    h, w = frame.shape[:2]
    
    if target_size:
        target_w, target_h = target_size
        frame = cv2.resize(frame, (target_w, target_h))
    elif max_size:
        # Resize to fit within max_size while maintaining aspect ratio
        scale = min(max_size / w, max_size / h)
        if scale < 1.0:
            new_w = int(w * scale)
            new_h = int(h * scale)
            frame = cv2.resize(frame, (new_w, new_h))
    
    return frame


def calculate_iou(box1: List[float], box2: List[float]) -> float:
    """Calculate IoU between two bounding boxes"""
    x1_1, y1_1, x2_1, y2_1 = box1
    x1_2, y1_2, x2_2, y2_2 = box2
    
    # Calculate intersection
    x1_i = max(x1_1, x1_2)
    y1_i = max(y1_1, y1_2)
    x2_i = min(x2_1, x2_2)
    y2_i = min(y2_1, y2_2)
    
    if x2_i <= x1_i or y2_i <= y1_i:
        return 0.0
    
    intersection = (x2_i - x1_i) * (y2_i - y1_i)
    
    # Calculate union
    area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
    area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
    union = area1 + area2 - intersection
    
    return intersection / union if union > 0 else 0.0


def filter_detections_by_area(detections: List[Detection], min_area: float = 100.0, 
                             max_area: float = None) -> List[Detection]:
    """Filter detections by bounding box area"""
    filtered = []
    
    for detection in detections:
        area = detection.area
        
        if area < min_area:
            continue
        
        if max_area and area > max_area:
            continue
        
        filtered.append(detection)
    
    return filtered


def non_max_suppression(detections: List[Detection], iou_threshold: float = 0.5) -> List[Detection]:
    """Apply non-maximum suppression to detections"""
    if not detections:
        return []
    
    # Sort by confidence
    detections = sorted(detections, key=lambda x: x.confidence, reverse=True)
    
    keep = []
    while detections:
        # Take detection with highest confidence
        current = detections.pop(0)
        keep.append(current)
        
        # Remove detections with high IoU
        remaining = []
        for detection in detections:
            iou = calculate_iou(current.bbox, detection.bbox)
            if iou <= iou_threshold:
                remaining.append(detection)
        
        detections = remaining
    
    return keep
