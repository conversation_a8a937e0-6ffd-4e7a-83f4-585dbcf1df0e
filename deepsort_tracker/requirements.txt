# Core dependencies for DeepSORT tracking system
opencv-python>=4.5.0
numpy>=1.20.0
scipy>=1.8.0

# Deep learning frameworks
torch>=1.10.0
torchvision>=0.11.0
onnxruntime-gpu>=1.10.0  # Use onnxruntime if GPU not available

# YOLO detection
ultralytics>=8.0.0

# Utilities
pillow>=9.0.0
psutil>=5.8.0  # For memory monitoring
tqdm>=4.64.0  # For progress bars

# Optional dependencies for enhanced functionality
# scikit-learn>=1.0.0  # For additional metrics
# matplotlib>=3.5.0  # For plotting
# seaborn>=0.11.0  # For visualization

# Development dependencies (optional)
# pytest>=7.0.0
# black>=22.0.0
# flake8>=4.0.0
