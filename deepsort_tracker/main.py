"""
Main application for DeepSORT tracking system with GPU optimization and multiprocessing
"""
import cv2
import argparse
import time
import logging
import signal
import sys
from typing import Optional
from .config import Config
from .multiprocess_tracker import MultiProcessTracker
from .detector import YOLODetector
from .feature_extractor import FeatureExtractor
from .deep_sort.tracker import DeepSORTTracker
from .deep_sort.nn_matching import NearestNeighborDistanceMetric
from .deep_sort.detection import Detection as DeepSortDetection
from .performance_monitor import PerformanceMonitor, Timer
from .utils import (
    setup_logging, draw_tracks, draw_performance_info, save_frame,
    create_video_writer, validate_video_input, resize_frame
)


class DeepSORTApp:
    """Main DeepSORT tracking application"""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Components
        self.multiprocess_tracker: Optional[MultiProcessTracker] = None
        self.detector: Optional[YOLODetector] = None
        self.feature_extractor: Optional[FeatureExtractor] = None
        self.tracker: Optional[DeepSORTTracker] = None
        
        # Video components
        self.video_capture: Optional[cv2.VideoCapture] = None
        self.video_writer: Optional[cv2.VideoWriter] = None
        
        # Performance monitoring
        self.performance_monitor = PerformanceMonitor(
            window_size=100,
            log_interval=config.logging.log_interval
        )
        
        # State
        self.running = False
        self.frame_count = 0
        self.start_time = None
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        self.logger.info(f"Received signal {signum}, shutting down...")
        self.running = False
    
    def initialize(self):
        """Initialize all components"""
        self.logger.info("Initializing DeepSORT tracking system...")
        
        # Validate configuration
        if not self.config.validate():
            raise RuntimeError("Configuration validation failed")
        
        # Create output directories
        self.config.create_output_dirs()
        
        # Initialize components based on processing mode
        if self.config.processing.enable_multiprocessing:
            self._initialize_multiprocess()
        else:
            self._initialize_single_process()
        
        # Initialize video components
        self._initialize_video()
        
        self.logger.info("Initialization completed successfully")
    
    def _initialize_multiprocess(self):
        """Initialize multiprocessing components"""
        self.multiprocess_tracker = MultiProcessTracker(self.config)
        self.multiprocess_tracker.start()
    
    def _initialize_single_process(self):
        """Initialize single process components"""
        # Initialize detector
        self.detector = YOLODetector(self.config.detection)
        
        # Initialize feature extractor
        self.feature_extractor = FeatureExtractor(self.config.feature)
        
        # Initialize tracker
        metric = NearestNeighborDistanceMetric(
            "cosine", self.config.tracking.max_cosine_distance,
            self.config.tracking.nn_budget
        )
        self.tracker = DeepSORTTracker(
            metric, self.config.tracking.max_iou_distance,
            self.config.tracking.max_age, self.config.tracking.n_init
        )
    
    def _initialize_video(self):
        """Initialize video input and output"""
        # Initialize video capture
        if self.config.video.input_path:
            # Validate video input
            valid, properties = validate_video_input(self.config.video.input_path)
            if not valid:
                raise RuntimeError(properties["error"])
            
            self.logger.info(f"Video properties: {properties}")
            
            self.video_capture = cv2.VideoCapture(self.config.video.input_path)
        else:
            # Use webcam
            self.video_capture = cv2.VideoCapture(0)
        
        if not self.video_capture.isOpened():
            raise RuntimeError("Failed to open video source")
        
        # Get frame properties
        frame_width = int(self.video_capture.get(cv2.CAP_PROP_FRAME_WIDTH))
        frame_height = int(self.video_capture.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        # Initialize video writer if output path is specified
        if self.config.video.output_path:
            self.video_writer = create_video_writer(
                self.config.video.output_path,
                self.config.video.output_fps,
                (frame_width, frame_height),
                self.config.video.output_codec
            )
    
    def run(self):
        """Main processing loop"""
        self.logger.info("Starting DeepSORT tracking...")
        self.running = True
        self.start_time = time.time()
        
        try:
            while self.running:
                success = self._process_frame()
                if not success:
                    break
                
                # Check frame limit
                if (self.config.video.max_frames and 
                    self.frame_count >= self.config.video.max_frames):
                    self.logger.info(f"Reached frame limit: {self.config.video.max_frames}")
                    break
                
                # Log performance periodically
                if self.config.logging.enable_performance_logging:
                    self.performance_monitor.log_performance()
        
        except KeyboardInterrupt:
            self.logger.info("Interrupted by user")
        except Exception as e:
            self.logger.error(f"Error during processing: {e}")
            raise
        finally:
            self._cleanup()
    
    def _process_frame(self) -> bool:
        """Process a single frame"""
        with Timer("total_frame_processing") as total_timer:
            # Read frame
            ret, frame = self.video_capture.read()
            if not ret:
                self.logger.info("End of video stream")
                return False
            
            self.frame_count += 1
            
            # Process frame based on mode
            if self.config.processing.enable_multiprocessing:
                result = self._process_frame_multiprocess(frame)
            else:
                result = self._process_frame_single(frame)
            
            if result:
                tracks, detections = result
                
                # Draw results
                annotated_frame = self._draw_results(frame, tracks, detections)
                
                # Display frame
                if self.config.video.display_video:
                    cv2.imshow('DeepSORT Tracking', annotated_frame)
                    if cv2.waitKey(1) & 0xFF == ord('q'):
                        return False
                
                # Save frame
                if self.video_writer:
                    self.video_writer.write(annotated_frame)
                
                if self.config.video.save_frames:
                    save_frame(annotated_frame, self.config.video.frame_save_dir, self.frame_count)
        
        # Record performance
        self.performance_monitor.record_total_time(total_timer.elapsed_time)
        
        return True
    
    def _process_frame_multiprocess(self, frame) -> Optional[tuple]:
        """Process frame using multiprocessing"""
        result = self.multiprocess_tracker.process_frame(self.frame_count, frame)
        if result:
            frame_id, tracks, detections = result
            return tracks, detections
        return None
    
    def _process_frame_single(self, frame) -> tuple:
        """Process frame in single process mode"""
        # Detection
        with Timer("detection") as det_timer:
            detections = self.detector.detect(frame)
        self.performance_monitor.record_detection_time(det_timer.elapsed_time, len(detections))
        
        # Feature extraction
        with Timer("feature_extraction") as feat_timer:
            detections_with_features = self.feature_extractor.extract_features(frame, detections)
        self.performance_monitor.record_feature_time(feat_timer.elapsed_time)
        
        # Tracking
        with Timer("tracking") as track_timer:
            # Convert to DeepSORT format
            deepsort_detections = self._convert_detections(detections_with_features)
            
            # Update tracker
            self.tracker.predict()
            tracks = self.tracker.update(deepsort_detections)
        
        self.performance_monitor.record_tracking_time(track_timer.elapsed_time, len(tracks))
        
        return tracks, detections_with_features
    
    def _convert_detections(self, detections):
        """Convert detector detections to DeepSORT format"""
        deepsort_detections = []
        
        for detection in detections:
            # Convert bbox from [x1, y1, x2, y2] to [x, y, w, h]
            x1, y1, x2, y2 = detection.bbox
            tlwh = [x1, y1, x2 - x1, y2 - y1]
            
            deepsort_detection = DeepSortDetection(
                tlwh=tlwh,
                confidence=detection.confidence,
                feature=detection.feature
            )
            deepsort_detections.append(deepsort_detection)
        
        return deepsort_detections
    
    def _draw_results(self, frame, tracks, detections):
        """Draw tracking results on frame"""
        # Draw tracks and detections
        annotated_frame = draw_tracks(frame, tracks, detections)
        
        # Calculate FPS
        elapsed_time = time.time() - self.start_time
        fps = self.frame_count / elapsed_time if elapsed_time > 0 else 0
        
        # Draw performance info
        annotated_frame = draw_performance_info(
            annotated_frame, fps, len(tracks), len(detections), self.frame_count
        )
        
        return annotated_frame
    
    def _cleanup(self):
        """Cleanup resources"""
        self.logger.info("Cleaning up resources...")
        
        # Stop multiprocessing tracker
        if self.multiprocess_tracker:
            self.multiprocess_tracker.stop()
        
        # Cleanup single process components
        if self.detector:
            self.detector.cleanup()
        if self.feature_extractor:
            self.feature_extractor.cleanup()
        
        # Release video resources
        if self.video_capture:
            self.video_capture.release()
        if self.video_writer:
            self.video_writer.release()
        
        # Close OpenCV windows
        cv2.destroyAllWindows()
        
        # Print performance summary
        if self.config.logging.enable_performance_logging:
            summary = self.performance_monitor.get_summary_report()
            self.logger.info(f"Performance Summary:\n{summary}")
        
        self.logger.info("Cleanup completed")


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='DeepSORT Multi-Object Tracking System')
    
    parser.add_argument('--video', type=str, help='Path to input video file')
    parser.add_argument('--output', type=str, help='Path to output video file')
    parser.add_argument('--config', type=str, help='Path to configuration file')
    parser.add_argument('--no-display', action='store_true', help='Disable video display')
    parser.add_argument('--no-multiprocess', action='store_true', help='Disable multiprocessing')
    parser.add_argument('--max-frames', type=int, help='Maximum number of frames to process')
    parser.add_argument('--log-level', type=str, default='INFO', 
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='Logging level')
    
    return parser.parse_args()


def main():
    """Main entry point"""
    args = parse_arguments()
    
    # Create configuration
    config = Config.from_env()
    
    # Override config with command line arguments
    if args.video:
        config.video.input_path = args.video
    if args.output:
        config.video.output_path = args.output
    if args.no_display:
        config.video.display_video = False
    if args.no_multiprocess:
        config.processing.enable_multiprocessing = False
    if args.max_frames:
        config.video.max_frames = args.max_frames
    if args.log_level:
        config.logging.level = args.log_level
    
    # Setup logging
    setup_logging(config)
    logger = logging.getLogger(__name__)
    
    try:
        # Create and run application
        app = DeepSORTApp(config)
        app.initialize()
        app.run()
        
    except Exception as e:
        logger.error(f"Application failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
