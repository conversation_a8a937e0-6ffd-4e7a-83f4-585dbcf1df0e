"""
Multiprocessing wrapper for DeepSORT tracking system
"""
import cv2
import numpy as np
import multiprocessing as mp
import queue
import time
import logging
from typing import List, Optional, Tuple
from .config import Config
from .detector import YOLODetector, Detection
from .feature_extractor import FeatureExtractor
from .deep_sort.tracker import DeepSORTTracker
from .deep_sort.nn_matching import NearestNeighborDistanceMetric
from .deep_sort.detection import Detection as DeepSortDetection
from .performance_monitor import PerformanceMonitor, Timer, get_memory_usage, get_gpu_memory_usage


logger = logging.getLogger(__name__)


class DetectionWorker(mp.Process):
    """Worker process for YOLO detection"""
    
    def __init__(self, config: Config, input_queue: mp.Queue, output_queue: mp.Queue, 
                 stop_event: mp.Event, worker_id: int):
        super().__init__()
        self.config = config
        self.input_queue = input_queue
        self.output_queue = output_queue
        self.stop_event = stop_event
        self.worker_id = worker_id
        self.detector = None
        
    def run(self):
        """Main worker loop"""
        try:
            # Initialize detector in worker process
            self.detector = YOLODetector(self.config.detection)
            logger.info(f"Detection worker {self.worker_id} initialized")
            
            while not self.stop_event.is_set():
                try:
                    # Get frame from input queue
                    frame_data = self.input_queue.get(timeout=1.0)
                    if frame_data is None:  # Shutdown signal
                        break
                    
                    frame_id, frame = frame_data
                    
                    # Run detection
                    detections = self.detector.detect(frame)
                    
                    # Send results to output queue
                    self.output_queue.put((frame_id, detections))
                    
                except queue.Empty:
                    continue
                except Exception as e:
                    logger.error(f"Detection worker {self.worker_id} error: {e}")
                    
        except Exception as e:
            logger.error(f"Detection worker {self.worker_id} initialization error: {e}")
        finally:
            if self.detector:
                self.detector.cleanup()
            logger.info(f"Detection worker {self.worker_id} stopped")


class TrackingWorker(mp.Process):
    """Worker process for tracking and feature extraction"""
    
    def __init__(self, config: Config, detection_queue: mp.Queue, output_queue: mp.Queue,
                 stop_event: mp.Event, frame_queue: mp.Queue):
        super().__init__()
        self.config = config
        self.detection_queue = detection_queue
        self.output_queue = output_queue
        self.stop_event = stop_event
        self.frame_queue = frame_queue
        
        self.feature_extractor = None
        self.tracker = None
        self.frame_buffer = {}
        
    def run(self):
        """Main tracking loop"""
        try:
            # Initialize components in worker process
            self.feature_extractor = FeatureExtractor(self.config.feature)
            
            # Initialize DeepSORT tracker
            metric = NearestNeighborDistanceMetric(
                "cosine", self.config.tracking.max_cosine_distance, 
                self.config.tracking.nn_budget)
            self.tracker = DeepSORTTracker(
                metric, self.config.tracking.max_iou_distance,
                self.config.tracking.max_age, self.config.tracking.n_init)
            
            logger.info("Tracking worker initialized")
            
            while not self.stop_event.is_set():
                try:
                    # Get detection results
                    detection_data = self.detection_queue.get(timeout=1.0)
                    if detection_data is None:  # Shutdown signal
                        break
                    
                    frame_id, detections = detection_data
                    
                    # Get corresponding frame
                    frame = self._get_frame(frame_id)
                    if frame is None:
                        continue
                    
                    # Extract features for detections
                    detections_with_features = self.feature_extractor.extract_features(frame, detections)
                    
                    # Convert to DeepSORT format
                    deepsort_detections = self._convert_detections(detections_with_features)
                    
                    # Update tracker
                    self.tracker.predict()
                    tracks = self.tracker.update(deepsort_detections)
                    
                    # Send tracking results
                    self.output_queue.put((frame_id, tracks, detections_with_features))
                    
                except queue.Empty:
                    continue
                except Exception as e:
                    logger.error(f"Tracking worker error: {e}")
                    
        except Exception as e:
            logger.error(f"Tracking worker initialization error: {e}")
        finally:
            if self.feature_extractor:
                self.feature_extractor.cleanup()
            logger.info("Tracking worker stopped")
    
    def _get_frame(self, frame_id: int) -> Optional[np.ndarray]:
        """Get frame from buffer or frame queue"""
        # Check if frame is already in buffer
        if frame_id in self.frame_buffer:
            frame = self.frame_buffer.pop(frame_id)
            return frame
        
        # Try to get frame from queue
        try:
            while True:
                frame_data = self.frame_queue.get_nowait()
                if frame_data is None:
                    break
                
                fid, frame = frame_data
                if fid == frame_id:
                    return frame
                else:
                    # Store for later use
                    self.frame_buffer[fid] = frame
                    
                # Limit buffer size
                if len(self.frame_buffer) > 10:
                    oldest_id = min(self.frame_buffer.keys())
                    del self.frame_buffer[oldest_id]
                    
        except queue.Empty:
            pass
        
        return None
    
    def _convert_detections(self, detections: List[Detection]) -> List[DeepSortDetection]:
        """Convert detector detections to DeepSORT format"""
        deepsort_detections = []
        
        for detection in detections:
            # Convert bbox from [x1, y1, x2, y2] to [x, y, w, h]
            x1, y1, x2, y2 = detection.bbox
            tlwh = [x1, y1, x2 - x1, y2 - y1]
            
            deepsort_detection = DeepSortDetection(
                tlwh=np.array(tlwh),
                confidence=detection.confidence,
                feature=detection.feature
            )
            deepsort_detections.append(deepsort_detection)
        
        return deepsort_detections


class MultiProcessTracker:
    """Main multiprocessing tracker coordinator"""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Multiprocessing components
        self.detection_workers = []
        self.tracking_worker = None
        self.stop_event = mp.Event()
        
        # Queues
        self.frame_queue = mp.Queue(maxsize=config.processing.queue_size)
        self.detection_input_queue = mp.Queue(maxsize=config.processing.queue_size)
        self.detection_output_queue = mp.Queue(maxsize=config.processing.queue_size)
        self.tracking_output_queue = mp.Queue(maxsize=config.processing.queue_size)
        self.frame_copy_queue = mp.Queue(maxsize=config.processing.queue_size)
        
        # Performance monitoring
        self.performance_monitor = PerformanceMonitor(
            window_size=100, 
            log_interval=config.logging.log_interval
        )
        
    def start(self):
        """Start all worker processes"""
        if not self.config.processing.enable_multiprocessing:
            self.logger.info("Multiprocessing disabled, using single process mode")
            return
        
        try:
            # Start detection workers
            for i in range(self.config.processing.num_detection_workers):
                worker = DetectionWorker(
                    self.config, self.detection_input_queue, 
                    self.detection_output_queue, self.stop_event, i
                )
                worker.start()
                self.detection_workers.append(worker)
            
            # Start tracking worker
            self.tracking_worker = TrackingWorker(
                self.config, self.detection_output_queue,
                self.tracking_output_queue, self.stop_event,
                self.frame_copy_queue
            )
            self.tracking_worker.start()
            
            self.logger.info(f"Started {len(self.detection_workers)} detection workers and 1 tracking worker")
            
        except Exception as e:
            self.logger.error(f"Failed to start workers: {e}")
            self.stop()
            raise
    
    def stop(self):
        """Stop all worker processes"""
        self.logger.info("Stopping multiprocess tracker...")
        
        # Signal stop
        self.stop_event.set()
        
        # Send shutdown signals to queues
        for _ in range(self.config.processing.num_detection_workers):
            try:
                self.detection_input_queue.put(None, timeout=1.0)
            except queue.Full:
                pass
        
        try:
            self.detection_output_queue.put(None, timeout=1.0)
        except queue.Full:
            pass
        
        # Wait for workers to finish
        for worker in self.detection_workers:
            worker.join(timeout=5.0)
            if worker.is_alive():
                worker.terminate()
        
        if self.tracking_worker:
            self.tracking_worker.join(timeout=5.0)
            if self.tracking_worker.is_alive():
                self.tracking_worker.terminate()
        
        self.logger.info("All workers stopped")
    
    def process_frame(self, frame_id: int, frame: np.ndarray) -> Optional[Tuple]:
        """Process a single frame through the pipeline"""
        try:
            # Record memory usage
            memory_mb, _ = get_memory_usage()
            gpu_memory_mb = get_gpu_memory_usage()
            self.performance_monitor.record_memory_usage(memory_mb, gpu_memory_mb)
            
            if self.config.processing.enable_multiprocessing:
                return self._process_frame_multiprocess(frame_id, frame)
            else:
                return self._process_frame_single(frame_id, frame)
                
        except Exception as e:
            self.logger.error(f"Error processing frame {frame_id}: {e}")
            return None
    
    def _process_frame_multiprocess(self, frame_id: int, frame: np.ndarray) -> Optional[Tuple]:
        """Process frame using multiprocessing"""
        try:
            # Send frame for detection
            self.detection_input_queue.put((frame_id, frame), timeout=1.0)
            
            # Send frame copy for tracking
            self.frame_copy_queue.put((frame_id, frame.copy()), timeout=1.0)
            
            # Get tracking results (non-blocking)
            try:
                result = self.tracking_output_queue.get_nowait()
                return result
            except queue.Empty:
                return None
                
        except queue.Full:
            self.logger.warning(f"Queue full, dropping frame {frame_id}")
            return None
    
    def _process_frame_single(self, frame_id: int, frame: np.ndarray) -> Tuple:
        """Process frame in single process mode"""
        # This would implement single-process version
        # For now, return empty result
        return (frame_id, [], [])
    
    def get_performance_stats(self) -> dict:
        """Get comprehensive performance statistics"""
        return self.performance_monitor.get_current_metrics()
    
    def log_performance(self):
        """Log current performance metrics"""
        self.performance_monitor.log_performance()
    
    def get_summary_report(self) -> str:
        """Get performance summary report"""
        return self.performance_monitor.get_summary_report()
