"""
Enhanced DeepSORT implementation for multi-object tracking
"""
from .tracker import DeepSORT<PERSON><PERSON>
from .track import Track, TrackState
from .detection import Detection
from .kalman_filter import Ka<PERSON>Filter
from .nn_matching import NearestNeighborDistanceMetric

__all__ = [
    'DeepSORTTracker',
    'Track',
    'TrackState',
    'Detection',
    'KalmanFilter',
    'NearestNeighborDistanceMetric'
]
