"""
IoU matching for DeepSORT tracking
"""
import numpy as np
from typing import List


def iou(bbox_a: np.ndar<PERSON>, bbox_b: np.ndarray) -> float:
    """Compute intersection over union.

    Parameters
    ----------
    bbox_a : ndarray
        A bounding box in format `(top left x, top left y, width, height)`.
    bbox_b : ndarray
        A bounding box in format `(top left x, top left y, width, height)`.

    Returns
    -------
    float
        The intersection over union in [0, 1].

    """
    if bbox_a[2] <= 0 or bbox_a[3] <= 0 or bbox_b[2] <= 0 or bbox_b[3] <= 0:
        return 0.0

    # Convert to (x1, y1, x2, y2) format
    a_x1, a_y1 = bbox_a[0], bbox_a[1]
    a_x2, a_y2 = bbox_a[0] + bbox_a[2], bbox_a[1] + bbox_a[3]
    
    b_x1, b_y1 = bbox_b[0], bbox_b[1]
    b_x2, b_y2 = bbox_b[0] + bbox_b[2], bbox_b[1] + bbox_b[3]

    # Compute intersection
    inter_x1 = max(a_x1, b_x1)
    inter_y1 = max(a_y1, b_y1)
    inter_x2 = min(a_x2, b_x2)
    inter_y2 = min(a_y2, b_y2)

    if inter_x2 <= inter_x1 or inter_y2 <= inter_y1:
        return 0.0

    inter_area = (inter_x2 - inter_x1) * (inter_y2 - inter_y1)
    
    # Compute union
    area_a = bbox_a[2] * bbox_a[3]
    area_b = bbox_b[2] * bbox_b[3]
    union_area = area_a + area_b - inter_area

    if union_area <= 0:
        return 0.0

    return inter_area / union_area


def iou_cost(tracks: List, detections: List, track_indices: List[int] = None, 
             detection_indices: List[int] = None) -> np.ndarray:
    """An intersection over union distance metric.

    Parameters
    ----------
    tracks : List[track.Track]
        A list of tracks.
    detections : List[detection.Detection]
        A list of detections.
    track_indices : Optional[List[int]]
        A list of indices to tracks that should be matched. Defaults to
        all `tracks`.
    detection_indices : Optional[List[int]]
        A list of indices to detections that should be matched. Defaults
        to all `detections`.

    Returns
    -------
    ndarray
        Returns a cost matrix of shape
        len(track_indices), len(detection_indices) where entry (i, j) is
        `1 - iou(tracks[track_indices[i]], detections[detection_indices[j]])`.

    """
    if track_indices is None:
        track_indices = np.arange(len(tracks))
    if detection_indices is None:
        detection_indices = np.arange(len(detections))

    cost_matrix = np.zeros((len(track_indices), len(detection_indices)))
    for row, track_idx in enumerate(track_indices):
        if tracks[track_idx].time_since_update > 1:
            cost_matrix[row, :] = 1e+5
            continue

        bbox = tracks[track_idx].to_tlwh()
        candidates = np.asarray([detections[i].tlwh for i in detection_indices])
        cost_matrix[row, :] = 1. - iou_batch(bbox, candidates)
    return cost_matrix


def iou_batch(bbox: np.ndarray, candidates: np.ndarray) -> np.ndarray:
    """Compute intersection over union between a bounding box and array of candidates.

    Parameters
    ----------
    bbox : ndarray
        A bounding box in format `(top left x, top left y, width, height)`.
    candidates : ndarray
        A matrix of candidate bounding boxes (one per row) in the same format
        as `bbox`.

    Returns
    -------
    ndarray
        The intersection over union in [0, 1] between the `bbox` and each
        candidate. A higher score means a larger fraction of the `bbox` is
        occluded by the candidate.

    """
    if len(candidates) == 0:
        return np.array([])

    bbox_tl, bbox_br = bbox[:2], bbox[:2] + bbox[2:]
    candidates_tl = candidates[:, :2]
    candidates_br = candidates[:, :2] + candidates[:, 2:]

    tl = np.c_[np.maximum(bbox_tl[0], candidates_tl[:, 0])[:, np.newaxis],
               np.maximum(bbox_tl[1], candidates_tl[:, 1])[:, np.newaxis]]
    br = np.c_[np.minimum(bbox_br[0], candidates_br[:, 0])[:, np.newaxis],
               np.minimum(bbox_br[1], candidates_br[:, 1])[:, np.newaxis]]
    wh = np.maximum(0., br - tl)

    area_intersection = wh.prod(axis=1)
    area_bbox = bbox[2:].prod()
    area_candidates = candidates[:, 2:].prod(axis=1)
    area_union = area_bbox + area_candidates - area_intersection
    
    # Avoid division by zero
    area_union = np.maximum(area_union, 1e-8)
    
    return area_intersection / area_union
