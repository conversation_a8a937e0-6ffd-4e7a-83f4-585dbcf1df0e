"""
Enhanced DeepSORT tracker implementation
"""
import numpy as np
import logging
from typing import List, Tuple
from .kalman_filter import Kalman<PERSON>ilter
from .linear_assignment import matching_cascade, min_cost_matching, gate_cost_matrix
from .iou_matching import iou_cost
from .nn_matching import NearestNeighborDistanceMetric
from .track import Track, TrackState
from .detection import Detection
from ..performance_monitor import Timer


logger = logging.getLogger(__name__)


class DeepSORTTracker:
    """
    Enhanced DeepSORT multi-target tracker.

    Parameters
    ----------
    metric : nn_matching.NearestNeighborDistanceMetric
        A distance metric for measurement-to-track association.
    max_age : int
        Maximum number of missed misses before a track is deleted.
    n_init : int
        Number of consecutive detections before the track is confirmed. The
        track state is set to `Deleted` if a miss occurs within the first
        `n_init` frames.
    max_iou_distance : float
        Maximum IoU distance for matching.

    Attributes
    ----------
    metric : nn_matching.NearestNeighborDistanceMetric
        The distance metric used for measurement to track association.
    max_age : int
        Maximum number of missed misses before a track is deleted.
    n_init : int
        Number of frames that a track remains in initialization phase.
    max_iou_distance : float
        Maximum IoU distance for matching.
    kf : kalman_filter.KalmanFilter
        A Kalman filter to filter target trajectories in image space.
    tracks : List[Track]
        The list of active tracks at the current time step.
    """

    def __init__(self, metric: NearestNeighborDistanceMetric, max_iou_distance: float = 0.7,
                 max_age: int = 70, n_init: int = 3):
        self.metric = metric
        self.max_iou_distance = max_iou_distance
        self.max_age = max_age
        self.n_init = n_init

        self.kf = KalmanFilter()
        self.tracks: List[Track] = []
        self._next_id = 1

        # Performance tracking
        self.total_updates = 0
        self.total_update_time = 0.0

        self.logger = logging.getLogger(__name__)

    def predict(self):
        """Propagate track state distributions one time step forward.

        This function should be called once every time step, before `update`.
        """
        for track in self.tracks:
            track.predict(self.kf)

    def update(self, detections: List[Detection]) -> List[Track]:
        """Perform measurement update and track management.

        Parameters
        ----------
        detections : List[detection.Detection]
            A list of detections at the current time step.

        Returns
        -------
        List[Track]
            Returns the list of active tracks.
        """
        with Timer("tracker_update") as timer:
            # Run matching cascade.
            matches, unmatched_tracks, unmatched_detections = \
                self._match(detections)

            # Update track set.
            for track_idx, detection_idx in matches:
                self.tracks[track_idx].update(
                    self.kf, detections[detection_idx])
            for track_idx in unmatched_tracks:
                self.tracks[track_idx].mark_missed()
            for detection_idx in unmatched_detections:
                self._initiate_track(detections[detection_idx])
            
            # Remove deleted tracks
            self.tracks = [t for t in self.tracks if not t.is_deleted()]

            # Update appearance samples of confirmed tracks.
            active_targets = [t.track_id for t in self.tracks if t.is_confirmed()]
            features, targets = [], []
            for track in self.tracks:
                if not track.is_confirmed():
                    continue
                features += track.features
                targets += [track.track_id for _ in track.features]
                track.features = []
            
            if features:
                self.metric.partial_fit(
                    np.asarray(features), np.asarray(targets), active_targets)

        # Update performance metrics
        self.total_update_time += timer.get_elapsed_ms()
        self.total_updates += 1

        self.logger.debug(f"Tracker update completed in {timer.get_elapsed_ms():.1f}ms. "
                         f"Active tracks: {len(self.tracks)}, Matches: {len(matches)}")

        return [t for t in self.tracks if t.is_confirmed()]

    def _match(self, detections: List[Detection]) -> Tuple[List, List, List]:
        """Perform track-detection association using matching cascade and IoU matching."""
        
        def gated_metric(tracks, dets, track_indices, detection_indices):
            features = np.array([dets[i].feature for i in detection_indices])
            targets = np.array([tracks[i].track_id for i in track_indices])
            cost_matrix = self.metric.distance(features, targets)
            cost_matrix = gate_cost_matrix(
                self.kf, cost_matrix, tracks, dets, track_indices,
                detection_indices)
            return cost_matrix

        # Split track set into confirmed and unconfirmed tracks.
        confirmed_tracks = [
            i for i, t in enumerate(self.tracks) if t.is_confirmed()]
        unconfirmed_tracks = [
            i for i, t in enumerate(self.tracks) if not t.is_confirmed()]

        # Associate confirmed tracks using appearance features.
        matches_a, unmatched_tracks_a, unmatched_detections = \
            matching_cascade(
                gated_metric, self.metric.matching_threshold, self.max_age,
                self.tracks, detections, confirmed_tracks)

        # Associate remaining tracks together with unconfirmed tracks using IOU.
        iou_track_candidates = unconfirmed_tracks + [
            k for k in unmatched_tracks_a if
            self.tracks[k].time_since_update == 1]
        unmatched_tracks_a = [
            k for k in unmatched_tracks_a if
            self.tracks[k].time_since_update != 1]
        matches_b, unmatched_tracks_b, unmatched_detections = \
            min_cost_matching(
                iou_cost, self.max_iou_distance, self.tracks,
                detections, iou_track_candidates, unmatched_detections)

        matches = matches_a + matches_b
        unmatched_tracks = list(set(unmatched_tracks_a + unmatched_tracks_b))
        return matches, unmatched_tracks, unmatched_detections

    def _initiate_track(self, detection: Detection):
        """Initialize a new track from an unmatched detection."""
        mean, covariance = self.kf.initiate(detection.to_xyah())
        track = Track(
            mean, covariance, self._next_id, self.n_init, self.max_age,
            detection.feature)
        self.tracks.append(track)
        self._next_id += 1

    def get_confirmed_tracks(self) -> List[Track]:
        """Get list of confirmed tracks."""
        return [t for t in self.tracks if t.is_confirmed()]

    def get_track_count(self) -> int:
        """Get total number of active tracks."""
        return len(self.tracks)

    def get_confirmed_track_count(self) -> int:
        """Get number of confirmed tracks."""
        return len(self.get_confirmed_tracks())

    def get_performance_stats(self) -> dict:
        """Get performance statistics."""
        avg_update_time = (
            self.total_update_time / max(1, self.total_updates)
            if self.total_updates > 0 else 0
        )
        
        return {
            "total_updates": self.total_updates,
            "total_update_time_ms": self.total_update_time,
            "average_update_time_ms": avg_update_time,
            "active_tracks": len(self.tracks),
            "confirmed_tracks": self.get_confirmed_track_count(),
            "max_age": self.max_age,
            "n_init": self.n_init,
            "max_iou_distance": self.max_iou_distance
        }

    def reset(self):
        """Reset tracker state."""
        self.tracks.clear()
        self._next_id = 1
        self.total_updates = 0
        self.total_update_time = 0.0
        self.logger.info("Tracker reset completed")
