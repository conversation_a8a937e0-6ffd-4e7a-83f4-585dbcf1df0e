import json

# Load the files
with open('info-04042025.json', 'r') as f:
    info_data = json.load(f)

with open('zones-04042025.json', 'r') as f:
    zone_data = json.load(f)

# Extract trackids from zones
zone_trackids = set()
for zone_name, zone_content in zone_data.items():
    for trackid in zone_content.keys():
        zone_trackids.add(int(trackid))

# Filter objects in each frame
for frame in info_data:
    # Keep only objects whose trackid is in zone_trackids
    frame['objects'] = [obj for obj in frame['objects'] 
                        if 'trackid' in obj and obj['trackid'] in zone_trackids]

# Save the updated info data
with open('info-04042025-filtered.json', 'w') as f:
    json.dump(info_data, f)

print(f"Filtered data saved to test-info-filtered.json")